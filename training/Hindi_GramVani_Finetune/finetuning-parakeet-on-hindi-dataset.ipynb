{"metadata": {"kernelspec": {"name": "python3", "display_name": "Python 3", "language": "python"}, "language_info": {"name": "python", "version": "3.11.11", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "colab": {"provenance": [], "gpuType": "T4"}, "accelerator": "GPU", "kaggle": {"accelerator": "gpu", "dataSources": [], "dockerImageVersionId": 31041, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "## Install Dependencies\n", "metadata": {"id": "1c3LZ3v7TPkt"}, "outputs": [], "execution_count": 1}, {"cell_type": "code", "source": "# # Install dependencies\n\n! git clone https://github.com/deepanshu-yadav/Hindi_GramVani_Finetune.git\n!pip install wget\n!apt-get install sox libsndfile1 ffmpeg libsox-fmt-mp3\n!pip install text-unidecode\n!pip install matplotlib>=3.3.2\n\n## Install NeMo\nBRANCH = 'main'\n!python -m pip install git+https://github.com/NVIDIA/NeMo.git@$BRANCH#egg=nemo_toolkit[all]", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VCBEYl-xSAPI", "outputId": "660701ac-11c3-4608-be8e-bc0682391690", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T11:59:05.089280Z", "iopub.execute_input": "2025-06-02T11:59:05.089478Z", "iopub.status.idle": "2025-06-02T12:03:28.726634Z", "shell.execute_reply.started": "2025-06-02T11:59:05.089452Z", "shell.execute_reply": "2025-06-02T12:03:28.725845Z"}}, "outputs": [{"name": "stdout", "text": "Cloning into 'Hindi_GramVani_Finetune'...\nremote: Enumerating objects: 18, done.\u001b[K\nremote: Counting objects: 100% (18/18), done.\u001b[K\nremote: Compressing objects: 100% (14/14), done.\u001b[K\nremote: Total 18 (delta 7), reused 15 (delta 4), pack-reused 0 (from 0)\u001b[K\nReceiving objects: 100% (18/18), 47.37 KiB | 703.00 KiB/s, done.\nResolving deltas: 100% (7/7), done.\nCollecting wget\n  Downloading wget-3.2.zip (10 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nBuilding wheels for collected packages: wget\n  Building wheel for wget (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for wget: filename=wget-3.2-py3-none-any.whl size=9655 sha256=71775be4ea1433353cfe8890b26a881ea2b6aa1c60ad2b929680fe8a5bc76ab3\n  Stored in directory: /root/.cache/pip/wheels/40/b3/0f/a40dbd1c6861731779f62cc4babcb234387e11d697df70ee97\nSuccessfully built wget\nInstalling collected packages: wget\nSuccessfully installed wget-3.2\nReading package lists... Done\nBuilding dependency tree... Done\nReading state information... Done\nlibsndfile1 is already the newest version (1.0.31-2ubuntu0.2).\nffmpeg is already the newest version (7:4.4.2-0ubuntu0.22.04.1).\nThe following additional packages will be installed:\n  libid3tag0 libmad0 libopencore-amrnb0 libopencore-amrwb0 libsox-fmt-alsa libsox-fmt-base libsox3\n  libwavpack1\nSuggested packages:\n  libsox-fmt-all\nThe following NEW packages will be installed:\n  libid3tag0 libmad0 libopencore-amrnb0 libopencore-amrwb0 libsox-fmt-alsa libsox-fmt-base\n  libsox-fmt-mp3 libsox3 libwavpack1 sox\n0 upgraded, 10 newly installed, 0 to remove and 87 not upgraded.\nNeed to get 728 kB of archives.\nAfter this operation, 2,151 kB of additional disk space will be used.\nGet:1 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libid3tag0 amd64 0.15.1b-14 [31.3 kB]\nGet:2 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libmad0 amd64 0.15.1b-10ubuntu1 [63.1 kB]\nGet:3 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libopencore-amrnb0 amd64 0.1.5-1 [94.8 kB]\nGet:4 http://archive.ubuntu.com/ubuntu jammy/universe amd64 libopencore-amrwb0 amd64 0.1.5-1 [49.1 kB]\nGet:5 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 libsox3 amd64 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 [240 kB]\nGet:6 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 libsox-fmt-alsa amd64 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 [11.2 kB]\nGet:7 http://archive.ubuntu.com/ubuntu jammy/main amd64 libwavpack1 amd64 5.4.0-1build2 [83.7 kB]\nGet:8 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 libsox-fmt-base amd64 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 [33.7 kB]\nGet:9 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 libsox-fmt-mp3 amd64 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 [17.3 kB]\nGet:10 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 sox amd64 14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1 [104 kB]\nFetched 728 kB in 2s (432 kB/s)\nSelecting previously unselected package libid3tag0:amd64.\n(Reading database ... 129184 files and directories currently installed.)\nPreparing to unpack .../0-libid3tag0_0.15.1b-14_amd64.deb ...\nUnpacking libid3tag0:amd64 (0.15.1b-14) ...\nSelecting previously unselected package libmad0:amd64.\nPreparing to unpack .../1-libmad0_0.15.1b-10ubuntu1_amd64.deb ...\nUnpacking libmad0:amd64 (0.15.1b-10ubuntu1) ...\nSelecting previously unselected package libopencore-amrnb0:amd64.\nPreparing to unpack .../2-libopencore-amrnb0_0.1.5-1_amd64.deb ...\nUnpacking libopencore-amrnb0:amd64 (0.1.5-1) ...\nSelecting previously unselected package libopencore-amrwb0:amd64.\nPreparing to unpack .../3-libopencore-amrwb0_0.1.5-1_amd64.deb ...\nUnpacking libopencore-amrwb0:amd64 (0.1.5-1) ...\nSelecting previously unselected package libsox3:amd64.\nPreparing to unpack .../4-libsox3_14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1_amd64.deb ...\nUnpacking libsox3:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSelecting previously unselected package libsox-fmt-alsa:amd64.\nPreparing to unpack .../5-libsox-fmt-alsa_14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1_amd64.deb ...\nUnpacking libsox-fmt-alsa:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSelecting previously unselected package libwavpack1:amd64.\nPreparing to unpack .../6-libwavpack1_5.4.0-1build2_amd64.deb ...\nUnpacking libwavpack1:amd64 (5.4.0-1build2) ...\nSelecting previously unselected package libsox-fmt-base:amd64.\nPreparing to unpack .../7-libsox-fmt-base_14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1_amd64.deb ...\nUnpacking libsox-fmt-base:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSelecting previously unselected package libsox-fmt-mp3:amd64.\nPreparing to unpack .../8-libsox-fmt-mp3_14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1_amd64.deb ...\nUnpacking libsox-fmt-mp3:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSelecting previously unselected package sox.\nPreparing to unpack .../9-sox_14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1_amd64.deb ...\nUnpacking sox (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSetting up libsox3:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSetting up libid3tag0:amd64 (0.15.1b-14) ...\nSetting up libopencore-amrwb0:amd64 (0.1.5-1) ...\nSetting up libsox-fmt-alsa:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSetting up libmad0:amd64 (0.15.1b-10ubuntu1) ...\nSetting up libwavpack1:amd64 (5.4.0-1build2) ...\nSetting up libopencore-amrnb0:amd64 (0.1.5-1) ...\nSetting up libsox-fmt-base:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSetting up libsox-fmt-mp3:amd64 (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nSetting up sox (14.4.2+git20190427-2+deb11u2ubuntu0.22.04.1) ...\nProcessing triggers for libc-bin (2.35-0ubuntu3.8) ...\n/sbin/ldconfig.real: /usr/local/lib/libumf.so.0 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtcm.so.1 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtcm_debug.so.1 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libhwloc.so.15 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libur_loader.so.0 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libur_adapter_level_zero.so.0 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libur_adapter_opencl.so.0 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_5.so.3 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbbbind.so.3 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbbbind_2_0.so.3 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbb.so.12 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc_proxy.so.2 is not a symbolic link\n\n/sbin/ldconfig.real: /usr/local/lib/libtbbmalloc.so.2 is not a symbolic link\n\nProcessing triggers for man-db (2.10.2-1) ...\nProcessing triggers for mailcap (3.70+nmu1ubuntu1) ...\nRequirement already satisfied: text-unidecode in /usr/local/lib/python3.11/dist-packages (1.3)\n\u001b[33mDEPRECATION: git+https://github.com/NVIDIA/NeMo.git@main#egg=nemo_toolkit[all] contains an egg fragment with a non-PEP 508 name pip 25.0 will enforce this behaviour change. A possible replacement is to use the req @ url syntax, and remove the egg fragment. Discussion can be found at https://github.com/pypa/pip/issues/11617\u001b[0m\u001b[33m\n\u001b[0mCollecting nemo_toolkit (from nemo_toolkit[all])\n  Cloning https://github.com/NVIDIA/NeMo.git (to revision main) to /tmp/pip-install-wa87pkcy/nemo-toolkit_d840b32ee0814ed29618f76155e7b73b\n  Running command git clone --filter=blob:none --quiet https://github.com/NVIDIA/NeMo.git /tmp/pip-install-wa87pkcy/nemo-toolkit_d840b32ee0814ed29618f76155e7b73b\n  Resolved https://github.com/NVIDIA/NeMo.git to commit 7e5912f0ed7d7422565147f4ec0ed984004901e8\n  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\nCollecting fsspec==2024.12.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading fsspec-2024.12.0-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: huggingface_hub>=0.24 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.31.1)\nRequirement already satisfied: numba in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.60.0)\nRequirement already satisfied: numpy>=1.22 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.26.4)\nRequirement already satisfied: onnx>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.17.0)\nCollecting protobuf~=4.24.4 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading protobuf-4.24.4-cp37-abi3-manylinux2014_x86_64.whl.metadata (540 bytes)\nRequirement already satisfied: python-dateutil in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.9.0.post0)\nCollecting ruamel.yaml (from nemo_toolkit->nemo_toolkit[all])\n  Downloading ruamel.yaml-0.18.12-py3-none-any.whl.metadata (24 kB)\nRequirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.2.2)\nRequirement already satisfied: setuptools>=70.0.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (75.2.0)\nRequirement already satisfied: tensorboard in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.18.0)\nRequirement already satisfied: text-unidecode in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.3)\nRequirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.6.0+cu124)\nRequirement already satisfied: tqdm>=4.41.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (4.67.1)\nRequirement already satisfied: wget in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.2)\nRequirement already satisfied: wrapt in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.17.2)\nCollecting black~=24.3 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading black-24.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl.metadata (79 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.2/79.2 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hRequirement already satisfied: click>=8.1 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (8.1.8)\nRequirement already satisfied: coverage in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (7.8.0)\nCollecting isort<6.0.0,>5.1.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading isort-5.13.2-py3-none-any.whl.metadata (12 kB)\nCollecting parameterized (from nemo_toolkit->nemo_toolkit[all])\n  Downloading parameterized-0.9.0-py2.py3-none-any.whl.metadata (18 kB)\nRequirement already satisfied: pytest in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (8.3.5)\nCollecting pytest-httpserver (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pytest_httpserver-1.1.3-py3-none-any.whl.metadata (6.2 kB)\nCollecting pytest-mock (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pytest_mock-3.14.1-py3-none-any.whl.metadata (3.9 kB)\nCollecting pytest-runner (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pytest_runner-6.0.1-py3-none-any.whl.metadata (7.3 kB)\nRequirement already satisfied: sphinx in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (8.2.3)\nCollecting sphinxcontrib-bibtex (from nemo_toolkit->nemo_toolkit[all])\n  Downloading sphinxcontrib_bibtex-2.6.3-py3-none-any.whl.metadata (6.3 kB)\nRequirement already satisfied: wandb in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.19.9)\nCollecting nemo_run (from nemo_toolkit->nemo_toolkit[all])\n  Downloading nemo_run-0.4.0-py3-none-any.whl.metadata (5.7 kB)\nRequirement already satisfied: cloudpickle in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.1.1)\nCollecting fiddle (from nemo_toolkit->nemo_toolkit[all])\n  Downloading fiddle-0.3.0-py3-none-any.whl.metadata (2.3 kB)\nCollecting hydra-core<=1.3.2,>1.3 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading hydra_core-1.3.2-py3-none-any.whl.metadata (5.5 kB)\nCollecting lightning<=2.4.0,>2.2.1 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading lightning-2.4.0-py3-none-any.whl.metadata (38 kB)\nRequirement already satisfied: omegaconf<=2.3 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.3.0)\nRequirement already satisfied: peft in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.14.0)\nRequirement already satisfied: torchmetrics>=0.11.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.7.1)\nRequirement already satisfied: transformers<=4.52.0,>=4.51.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (4.51.3)\nCollecting webdataset>=0.2.86 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading webdataset-0.2.111-py3-none-any.whl.metadata (15 kB)\nCollecting bitsandbytes==0.45.3 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading bitsandbytes-0.45.3-py3-none-manylinux_2_24_x86_64.whl.metadata (5.0 kB)\nRequirement already satisfied: datasets in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.6.0)\nRequirement already satisfied: einops in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.8.1)\nRequirement already satisfied: inflect in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (7.5.0)\nCollecting mediapy==1.1.6 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading mediapy-1.1.6-py3-none-any.whl.metadata (4.8 kB)\nRequirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.2.3)\nCollecting sacremoses>=0.0.43 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading sacremoses-0.1.1-py3-none-any.whl.metadata (8.3 kB)\nRequirement already satisfied: sentencepiece<1.0.0 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.2.0)\nCollecting braceexpand (from nemo_toolkit->nemo_toolkit[all])\n  Downloading braceexpand-0.1.7-py2.py3-none-any.whl.metadata (3.0 kB)\nRequirement already satisfied: editdistance in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.8.1)\nCollecting g2p_en (from nemo_toolkit->nemo_toolkit[all])\n  Downloading g2p_en-2.1.0-py3-none-any.whl.metadata (4.5 kB)\nCollecting jiwer (from nemo_toolkit->nemo_toolkit[all])\n  Downloading jiwer-3.1.0-py3-none-any.whl.metadata (2.6 kB)\nCollecting kaldi-python-io (from nemo_toolkit->nemo_toolkit[all])\n  Downloading kaldi-python-io-1.2.2.tar.gz (8.8 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting kaldiio (from nemo_toolkit->nemo_toolkit[all])\n  Downloading kaldiio-2.18.1-py3-none-any.whl.metadata (13 kB)\nCollecting lhotse!=1.31.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading lhotse-1.30.3-py3-none-any.whl.metadata (18 kB)\nRequirement already satisfied: librosa>=0.10.1 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.11.0)\nRequirement already satisfied: marshmallow in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.26.1)\nRequirement already satisfied: optuna in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (4.3.0)\nRequirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (25.0)\nCollecting pyannote.core (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pyannote.core-5.0.0-py3-none-any.whl.metadata (1.4 kB)\nCollecting pyannote.metrics (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pyannote.metrics-3.2.1-py3-none-any.whl.metadata (1.3 kB)\nRequirement already satisfied: pydub in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.25.1)\nCollecting pyloudnorm (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pyloudnorm-0.1.1-py3-none-any.whl.metadata (5.6 kB)\nCollecting resampy (from nemo_toolkit->nemo_toolkit[all])\n  Downloading resampy-0.4.3-py3-none-any.whl.metadata (3.0 kB)\nRequirement already satisfied: scipy>=0.14 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.15.2)\nRequirement already satisfied: soundfile in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.13.1)\nCollecting sox<=1.5.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading sox-1.5.0.tar.gz (63 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63.9/63.9 kB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting texterrors<1.0.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading texterrors-0.5.1.tar.gz (23 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting whisper_normalizer (from nemo_toolkit->nemo_toolkit[all])\n  Downloading whisper_normalizer-0.1.11-py3-none-any.whl.metadata (7.9 kB)\nCollecting num2words (from nemo_toolkit->nemo_toolkit[all])\n  Downloading num2words-0.5.14-py3-none-any.whl.metadata (13 kB)\nCollecting accelerated-scan (from nemo_toolkit->nemo_toolkit[all])\n  Downloading accelerated_scan-0.2.0-py3-none-any.whl.metadata (5.3 kB)\nRequirement already satisfied: boto3 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (1.38.11)\nCollecting faiss-cpu (from nemo_toolkit->nemo_toolkit[all])\n  Downloading faiss_cpu-1.11.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (4.8 kB)\nCollecting flask_restful (from nemo_toolkit->nemo_toolkit[all])\n  Downloading Flask_RESTful-0.3.10-py2.py3-none-any.whl.metadata (1.0 kB)\nCollecting ftfy (from nemo_toolkit->nemo_toolkit[all])\n  Downloading ftfy-6.3.1-py3-none-any.whl.metadata (7.3 kB)\nRequirement already satisfied: gdown in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (5.2.0)\nRequirement already satisfied: h5py in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.13.0)\nCollecting ijson (from nemo_toolkit->nemo_toolkit[all])\n  Downloading ijson-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)\nRequirement already satisfied: jieba in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.42.1)\nCollecting markdown2 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading markdown2-2.5.3-py3-none-any.whl.metadata (2.1 kB)\nRequirement already satisfied: matplotlib>=3.3.2 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.7.2)\nCollecting megatron_core (from nemo_toolkit->nemo_toolkit[all])\n  Downloading megatron_core-0.12.1-cp311-cp311-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl.metadata (74 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m74.1/74.1 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hRequirement already satisfied: nltk>=3.6.5 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.9.1)\nCollecting nvidia-modelopt<=0.29.0,>=0.27.0 (from nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_modelopt-0.29.0-py3-none-manylinux_2_28_x86_64.whl.metadata (9.8 kB)\nCollecting nvidia-resiliency-ext<1.0.0,>=0.3.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_resiliency_ext-0.4.0-cp311-cp311-manylinux_2_31_x86_64.whl.metadata (4.4 kB)\nRequirement already satisfied: nvtx in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.2.11)\nCollecting opencc (from nemo_toolkit->nemo_toolkit[all])\n  Downloading OpenCC-1.1.9-cp311-cp311-manylinux2014_x86_64.whl.metadata (13 kB)\nCollecting pangu (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pangu-*******-py3-none-any.whl.metadata (5.3 kB)\nRequirement already satisfied: prettytable in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.16.0)\nCollecting rapidfuzz (from nemo_toolkit->nemo_toolkit[all])\n  Downloading rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)\nCollecting rouge_score (from nemo_toolkit->nemo_toolkit[all])\n  Downloading rouge_score-0.1.2.tar.gz (17 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting sacrebleu (from nemo_toolkit->nemo_toolkit[all])\n  Downloading sacrebleu-2.5.1-py3-none-any.whl.metadata (51 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.8/51.8 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hRequirement already satisfied: sentence_transformers in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (3.4.1)\nCollecting tensorstore<0.1.72 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading tensorstore-0.1.71-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)\nCollecting tiktoken==0.7.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading tiktoken-0.7.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)\nCollecting unstructured==0.14.9 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading unstructured-0.14.9-py3-none-any.whl.metadata (28 kB)\nCollecting zarr<3.0.0,>=2.18.2 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading zarr-2.18.7-py3-none-any.whl.metadata (5.8 kB)\nCollecting attrdict (from nemo_toolkit->nemo_toolkit[all])\n  Downloading attrdict-2.0.1-py2.py3-none-any.whl.metadata (6.7 kB)\nCollecting cdifflib==1.2.6 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading cdifflib-1.2.6.tar.gz (11 kB)\n  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: janome in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.5.0)\nRequirement already satisfied: kornia in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.8.1)\nCollecting nemo_text_processing (from nemo_toolkit->nemo_toolkit[all])\n  Downloading nemo_text_processing-1.1.0-py3-none-any.whl.metadata (7.3 kB)\nCollecting pypinyin (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pypinyin-0.54.0-py2.py3-none-any.whl.metadata (12 kB)\nCollecting pypinyin-dict (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pypinyin_dict-0.9.0-py2.py3-none-any.whl.metadata (3.8 kB)\nRequirement already satisfied: seaborn in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.12.2)\nCollecting progress>=1.5 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading progress-1.6.tar.gz (7.8 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: tabulate>=0.8.7 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.9.0)\nCollecting textdistance>=4.1.5 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading textdistance-4.6.3-py3-none-any.whl.metadata (18 kB)\nCollecting addict (from nemo_toolkit->nemo_toolkit[all])\n  Downloading addict-2.4.0-py3-none-any.whl.metadata (1.0 kB)\nCollecting clip (from nemo_toolkit->nemo_toolkit[all])\n  Downloading clip-0.2.0.tar.gz (5.5 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting decord (from nemo_toolkit->nemo_toolkit[all])\n  Downloading decord-0.6.0-py3-none-manylinux2010_x86_64.whl.metadata (422 bytes)\nRequirement already satisfied: diffusers>=0.19.3 in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (0.32.2)\nCollecting einops_exts (from nemo_toolkit->nemo_toolkit[all])\n  Downloading einops_exts-0.0.4-py3-none-any.whl.metadata (621 bytes)\nRequirement already satisfied: imageio in /usr/local/lib/python3.11/dist-packages (from nemo_toolkit->nemo_toolkit[all]) (2.37.0)\nCollecting megatron-energon==5.2.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading megatron_energon-5.2.0-py3-none-any.whl.metadata (5.1 kB)\nCollecting nerfacc>=0.5.3 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading nerfacc-0.5.3-py3-none-any.whl.metadata (915 bytes)\nCollecting open_clip_torch==2.24.0 (from nemo_toolkit->nemo_toolkit[all])\n  Downloading open_clip_torch-2.24.0-py3-none-any.whl.metadata (30 kB)\nCollecting qwen_vl_utils (from nemo_toolkit->nemo_toolkit[all])\n  Downloading qwen_vl_utils-0.0.11-py3-none-any.whl.metadata (6.3 kB)\nCollecting taming-transformers (from nemo_toolkit->nemo_toolkit[all])\n  Downloading taming_transformers-0.0.1-py3-none-any.whl.metadata (499 bytes)\nCollecting torchdiffeq (from nemo_toolkit->nemo_toolkit[all])\n  Downloading torchdiffeq-0.2.5-py3-none-any.whl.metadata (440 bytes)\nCollecting torchsde (from nemo_toolkit->nemo_toolkit[all])\n  Downloading torchsde-0.2.6-py3-none-any.whl.metadata (5.3 kB)\nCollecting trimesh (from nemo_toolkit->nemo_toolkit[all])\n  Downloading trimesh-4.6.10-py3-none-any.whl.metadata (18 kB)\nCollecting pesq (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pesq-0.0.4.tar.gz (38 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting pystoi (from nemo_toolkit->nemo_toolkit[all])\n  Downloading pystoi-0.4.1-py2.py3-none-any.whl.metadata (4.0 kB)\nCollecting nvidia-lm-eval (from nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_lm_eval-25.4.1-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: ipython in /usr/local/lib/python3.11/dist-packages (from mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (7.34.0)\nRequirement already satisfied: Pillow in /usr/local/lib/python3.11/dist-packages (from mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (11.1.0)\nCollecting multi-storage-client>=0.13.0 (from megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading multi_storage_client-0.21.0-py3-none-any.whl.metadata (3.1 kB)\nRequirement already satisfied: pyyaml in /usr/local/lib/python3.11/dist-packages (from megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (6.0.2)\nRequirement already satisfied: s3fs in /usr/local/lib/python3.11/dist-packages (from megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (0.4.2)\nRequirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (from open_clip_torch==2.24.0->nemo_toolkit->nemo_toolkit[all]) (0.21.0+cu124)\nRequirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from open_clip_torch==2.24.0->nemo_toolkit->nemo_toolkit[all]) (2024.11.6)\nRequirement already satisfied: timm in /usr/local/lib/python3.11/dist-packages (from open_clip_torch==2.24.0->nemo_toolkit->nemo_toolkit[all]) (1.0.15)\nRequirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.11/dist-packages (from tiktoken==0.7.0->nemo_toolkit->nemo_toolkit[all]) (2.32.3)\nRequirement already satisfied: chardet in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (5.2.0)\nCollecting filetype (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\nCollecting python-magic (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading python_magic-0.4.27-py2.py3-none-any.whl.metadata (5.8 kB)\nRequirement already satisfied: lxml in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (5.3.1)\nRequirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (4.13.3)\nRequirement already satisfied: emoji in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (2.14.1)\nRequirement already satisfied: dataclasses-json in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (0.6.7)\nCollecting python-iso639 (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading python_iso639-2025.2.18-py3-none-any.whl.metadata (14 kB)\nCollecting langdetect (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading langdetect-1.0.9.tar.gz (981 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m19.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting backoff (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\nRequirement already satisfied: typing-extensions in /usr/local/lib/python3.11/dist-packages (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (4.13.2)\nCollecting unstructured-client (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading unstructured_client-0.36.0-py3-none-any.whl.metadata (21 kB)\nRequirement already satisfied: mypy-extensions>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from black~=24.3->nemo_toolkit->nemo_toolkit[all]) (1.1.0)\nCollecting pathspec>=0.9.0 (from black~=24.3->nemo_toolkit->nemo_toolkit[all])\n  Downloading pathspec-0.12.1-py3-none-any.whl.metadata (21 kB)\nRequirement already satisfied: platformdirs>=2 in /usr/local/lib/python3.11/dist-packages (from black~=24.3->nemo_toolkit->nemo_toolkit[all]) (4.3.8)\nRequirement already satisfied: importlib-metadata in /usr/local/lib/python3.11/dist-packages (from diffusers>=0.19.3->nemo_toolkit->nemo_toolkit[all]) (8.7.0)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from diffusers>=0.19.3->nemo_toolkit->nemo_toolkit[all]) (3.18.0)\nRequirement already satisfied: safetensors>=0.3.1 in /usr/local/lib/python3.11/dist-packages (from diffusers>=0.19.3->nemo_toolkit->nemo_toolkit[all]) (0.5.3)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.24->nemo_toolkit->nemo_toolkit[all]) (1.1.0)\nRequirement already satisfied: antlr4-python3-runtime==4.9.* in /usr/local/lib/python3.11/dist-packages (from hydra-core<=1.3.2,>1.3->nemo_toolkit->nemo_toolkit[all]) (4.9.3)\nRequirement already satisfied: audioread>=2.1.9 in /usr/local/lib/python3.11/dist-packages (from lhotse!=1.31.0->nemo_toolkit->nemo_toolkit[all]) (3.0.1)\nRequirement already satisfied: cytoolz>=0.10.1 in /usr/local/lib/python3.11/dist-packages (from lhotse!=1.31.0->nemo_toolkit->nemo_toolkit[all]) (1.0.1)\nCollecting intervaltree>=3.1.0 (from lhotse!=1.31.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading intervaltree-3.1.0.tar.gz (32 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting lilcom>=1.1.0 (from lhotse!=1.31.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading lilcom-1.8.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.2 kB)\nRequirement already satisfied: joblib>=1.0 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (1.5.0)\nRequirement already satisfied: decorator>=4.3.0 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (4.4.2)\nRequirement already satisfied: pooch>=1.1 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (1.8.2)\nRequirement already satisfied: soxr>=0.3.2 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (0.5.0.post1)\nRequirement already satisfied: lazy_loader>=0.1 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (0.4)\nRequirement already satisfied: msgpack>=1.0 in /usr/local/lib/python3.11/dist-packages (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all]) (1.1.0)\nRequirement already satisfied: lightning-utilities<2.0,>=0.10.0 in /usr/local/lib/python3.11/dist-packages (from lightning<=2.4.0,>2.2.1->nemo_toolkit->nemo_toolkit[all]) (0.14.3)\nCollecting packaging (from nemo_toolkit->nemo_toolkit[all])\n  Downloading packaging-24.2-py3-none-any.whl.metadata (3.2 kB)\nRequirement already satisfied: pytorch-lightning in /usr/local/lib/python3.11/dist-packages (from lightning<=2.4.0,>2.2.1->nemo_toolkit->nemo_toolkit[all]) (2.5.1.post0)\nRequirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.2->nemo_toolkit->nemo_toolkit[all]) (1.3.1)\nRequirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.2->nemo_toolkit->nemo_toolkit[all]) (0.12.1)\nRequirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.2->nemo_toolkit->nemo_toolkit[all]) (4.57.0)\nRequirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.2->nemo_toolkit->nemo_toolkit[all]) (1.4.8)\nRequirement already satisfied: pyparsing<3.1,>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.2->nemo_toolkit->nemo_toolkit[all]) (3.0.9)\nRequirement already satisfied: rich>=12 in /usr/local/lib/python3.11/dist-packages (from nerfacc>=0.5.3->nemo_toolkit->nemo_toolkit[all]) (14.0.0)\nRequirement already satisfied: llvmlite<0.44,>=0.43.0dev0 in /usr/local/lib/python3.11/dist-packages (from numba->nemo_toolkit->nemo_toolkit[all]) (0.43.0)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2025.1.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2022.1.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2.4.1)\nCollecting nvidia-modelopt-core==0.29.0 (from nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_modelopt_core-0.29.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (847 bytes)\nRequirement already satisfied: ninja in /usr/local/lib/python3.11/dist-packages (from nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (1.11.1.4)\nRequirement already satisfied: pydantic>=2.0 in /usr/local/lib/python3.11/dist-packages (from nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (2.11.4)\nCollecting pulp (from nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all])\n  Downloading pulp-3.2.1-py3-none-any.whl.metadata (6.9 kB)\nRequirement already satisfied: pynvml>=11.5.0 in /usr/local/lib/python3.11/dist-packages (from nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (12.0.0)\nCollecting torchprofile>=0.0.4 (from nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all])\n  Downloading torchprofile-0.0.4-py3-none-any.whl.metadata (303 bytes)\nRequirement already satisfied: defusedxml in /usr/local/lib/python3.11/dist-packages (from nvidia-resiliency-ext<1.0.0,>=0.3.0->nemo_toolkit->nemo_toolkit[all]) (0.7.1)\nRequirement already satisfied: nvidia-ml-py>=12.570.86 in /usr/local/lib/python3.11/dist-packages (from nvidia-resiliency-ext<1.0.0,>=0.3.0->nemo_toolkit->nemo_toolkit[all]) (12.575.51)\nRequirement already satisfied: psutil>=6.0.0 in /usr/local/lib/python3.11/dist-packages (from nvidia-resiliency-ext<1.0.0,>=0.3.0->nemo_toolkit->nemo_toolkit[all]) (7.0.0)\nRequirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil->nemo_toolkit->nemo_toolkit[all]) (1.17.0)\nRequirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->nemo_toolkit->nemo_toolkit[all]) (3.6.0)\nRequirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.11/dist-packages (from soundfile->nemo_toolkit->nemo_toolkit[all]) (1.17.1)\nRequirement already satisfied: ml_dtypes>=0.3.1 in /usr/local/lib/python3.11/dist-packages (from tensorstore<0.1.72->nemo_toolkit->nemo_toolkit[all]) (0.4.1)\nRequirement already satisfied: pybind11 in /usr/local/lib/python3.11/dist-packages (from texterrors<1.0.0->nemo_toolkit->nemo_toolkit[all]) (2.13.6)\nCollecting plac (from texterrors<1.0.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading plac-1.4.5-py2.py3-none-any.whl.metadata (5.9 kB)\nCollecting loguru (from texterrors<1.0.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading loguru-0.7.3-py3-none-any.whl.metadata (22 kB)\nRequirement already satisfied: termcolor in /usr/local/lib/python3.11/dist-packages (from texterrors<1.0.0->nemo_toolkit->nemo_toolkit[all]) (3.0.1)\nCollecting Levenshtein (from texterrors<1.0.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading levenshtein-0.27.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (3.4.2)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (3.1.6)\nRequirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (12.4.127)\nRequirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (12.4.127)\nRequirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (12.4.127)\nCollecting nvidia-cudnn-cu12==******** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cublas-cu12==******** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cufft-cu12==******** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-curand-cu12==********** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cusolver-cu12==******** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cusparse-cu12==********** (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (12.4.127)\nCollecting nvidia-nvjitlink-cu12==12.4.127 (from torch->nemo_toolkit->nemo_toolkit[all])\n  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->nemo_toolkit->nemo_toolkit[all]) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->nemo_toolkit->nemo_toolkit[all]) (1.3.0)\nRequirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers<=4.52.0,>=4.51.0->nemo_toolkit->nemo_toolkit[all]) (0.21.1)\nCollecting asciitree (from zarr<3.0.0,>=2.18.2->nemo_toolkit->nemo_toolkit[all])\n  Downloading asciitree-0.3.3.tar.gz (4.0 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting fasteners (from zarr<3.0.0,>=2.18.2->nemo_toolkit->nemo_toolkit[all])\n  Downloading fasteners-0.19-py3-none-any.whl.metadata (4.9 kB)\nCollecting numcodecs!=0.14.0,!=0.14.1,<0.16,>=0.10.0 (from zarr<3.0.0,>=2.18.2->nemo_toolkit->nemo_toolkit[all])\n  Downloading numcodecs-0.15.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.9 kB)\nRequirement already satisfied: botocore<1.39.0,>=1.38.11 in /usr/local/lib/python3.11/dist-packages (from boto3->nemo_toolkit->nemo_toolkit[all]) (1.38.11)\nRequirement already satisfied: jmespath<2.0.0,>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from boto3->nemo_toolkit->nemo_toolkit[all]) (1.0.1)\nRequirement already satisfied: s3transfer<0.13.0,>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from boto3->nemo_toolkit->nemo_toolkit[all]) (0.12.0)\nRequirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets->nemo_toolkit->nemo_toolkit[all]) (19.0.1)\nRequirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from datasets->nemo_toolkit->nemo_toolkit[all]) (0.3.8)\nRequirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets->nemo_toolkit->nemo_toolkit[all]) (3.5.0)\nRequirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets->nemo_toolkit->nemo_toolkit[all]) (0.70.16)\nRequirement already satisfied: absl-py in /usr/local/lib/python3.11/dist-packages (from fiddle->nemo_toolkit->nemo_toolkit[all]) (1.4.0)\nRequirement already satisfied: graphviz in /usr/local/lib/python3.11/dist-packages (from fiddle->nemo_toolkit->nemo_toolkit[all]) (0.20.3)\nCollecting libcst (from fiddle->nemo_toolkit->nemo_toolkit[all])\n  Downloading libcst-1.8.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (16 kB)\nCollecting aniso8601>=0.82 (from flask_restful->nemo_toolkit->nemo_toolkit[all])\n  Downloading aniso8601-10.0.1-py2.py3-none-any.whl.metadata (23 kB)\nRequirement already satisfied: Flask>=0.8 in /usr/local/lib/python3.11/dist-packages (from flask_restful->nemo_toolkit->nemo_toolkit[all]) (3.1.0)\nRequirement already satisfied: pytz in /usr/local/lib/python3.11/dist-packages (from flask_restful->nemo_toolkit->nemo_toolkit[all]) (2025.2)\nRequirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from ftfy->nemo_toolkit->nemo_toolkit[all]) (0.2.13)\nCollecting distance>=0.1.3 (from g2p_en->nemo_toolkit->nemo_toolkit[all])\n  Downloading Distance-0.1.3.tar.gz (180 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m180.3/180.3 kB\u001b[0m \u001b[31m10.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: more_itertools>=8.5.0 in /usr/local/lib/python3.11/dist-packages (from inflect->nemo_toolkit->nemo_toolkit[all]) (10.6.0)\nRequirement already satisfied: typeguard>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from inflect->nemo_toolkit->nemo_toolkit[all]) (4.4.2)\nRequirement already satisfied: kornia_rs>=0.1.9 in /usr/local/lib/python3.11/dist-packages (from kornia->nemo_toolkit->nemo_toolkit[all]) (0.1.9)\nCollecting pytest-cov (from megatron_core->nemo_toolkit->nemo_toolkit[all])\n  Downloading pytest_cov-6.1.1-py3-none-any.whl.metadata (28 kB)\nCollecting pytest-random-order (from megatron_core->nemo_toolkit->nemo_toolkit[all])\n  Downloading pytest_random_order-1.1.1-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: catalogue>=2.0.10 in /usr/local/lib/python3.11/dist-packages (from nemo_run->nemo_toolkit->nemo_toolkit[all]) (2.0.10)\nCollecting cryptography<43.0.0 (from nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading cryptography-42.0.8-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (5.3 kB)\nCollecting fabric>=3.2.2 (from nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading fabric-3.2.2-py3-none-any.whl.metadata (3.5 kB)\nCollecting inquirerpy>=0.3.4 (from nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading InquirerPy-0.3.4-py3-none-any.whl.metadata (8.1 kB)\nCollecting torchx>=0.7.0 (from nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading torchx-0.7.0-py3-none-any.whl.metadata (6.2 kB)\nRequirement already satisfied: typer>=0.12.3 in /usr/local/lib/python3.11/dist-packages (from nemo_run->nemo_toolkit->nemo_toolkit[all]) (0.15.2)\nCollecting pynini==2.1.6.post1 (from nemo_text_processing->nemo_toolkit->nemo_toolkit[all])\n  Downloading pynini-2.1.6.post1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (4.8 kB)\nCollecting docopt>=0.6.2 (from num2words->nemo_toolkit->nemo_toolkit[all])\n  Downloading docopt-0.6.2.tar.gz (25 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting evaluate (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading evaluate-0.4.3-py3-none-any.whl.metadata (9.2 kB)\nCollecting jsonlines (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading jsonlines-4.0.0-py3-none-any.whl.metadata (1.6 kB)\nRequirement already satisfied: numexpr in /usr/local/lib/python3.11/dist-packages (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (2.10.2)\nCollecting pytablewriter (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading pytablewriter-1.2.1-py3-none-any.whl.metadata (38 kB)\nCollecting tqdm-multiprocess (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading tqdm_multiprocess-0.0.11-py3-none-any.whl.metadata (5.7 kB)\nRequirement already satisfied: zstandard in /usr/local/lib/python3.11/dist-packages (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (0.23.0)\nCollecting word2number (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading word2number-1.1.zip (9.7 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nCollecting httpx==0.27.0 (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading httpx-0.27.0-py3-none-any.whl.metadata (7.2 kB)\nCollecting immutabledict==4.2.0 (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading immutabledict-4.2.0-py3-none-any.whl.metadata (3.4 kB)\nRequirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (3.11.18)\nRequirement already satisfied: tenacity in /usr/local/lib/python3.11/dist-packages (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (9.1.2)\nCollecting beautifulsoup4 (from unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading beautifulsoup4-4.13.1-py3-none-any.whl.metadata (3.8 kB)\nCollecting openai==1.61.0 (from nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading openai-1.61.0-py3-none-any.whl.metadata (27 kB)\nRequirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (2.6)\nRequirement already satisfied: anyio in /usr/local/lib/python3.11/dist-packages (from httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (4.9.0)\nRequirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (2025.4.26)\nRequirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.0.7)\nRequirement already satisfied: idna in /usr/local/lib/python3.11/dist-packages (from httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (3.10)\nRequirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.3.1)\nRequirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai==1.61.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.9.0)\nRequirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai==1.61.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (0.9.0)\nRequirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx==0.27.0->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (0.14.0)\nCollecting portalocker (from sacrebleu->nemo_toolkit->nemo_toolkit[all])\n  Downloading portalocker-3.1.1-py3-none-any.whl.metadata (8.6 kB)\nRequirement already satisfied: colorama in /usr/local/lib/python3.11/dist-packages (from sacrebleu->nemo_toolkit->nemo_toolkit[all]) (0.4.6)\nRequirement already satisfied: alembic>=1.5.0 in /usr/local/lib/python3.11/dist-packages (from optuna->nemo_toolkit->nemo_toolkit[all]) (1.15.2)\nRequirement already satisfied: colorlog in /usr/local/lib/python3.11/dist-packages (from optuna->nemo_toolkit->nemo_toolkit[all]) (6.9.0)\nRequirement already satisfied: sqlalchemy>=1.4.2 in /usr/local/lib/python3.11/dist-packages (from optuna->nemo_toolkit->nemo_toolkit[all]) (2.0.40)\nRequirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->nemo_toolkit->nemo_toolkit[all]) (2025.2)\nRequirement already satisfied: accelerate>=0.21.0 in /usr/local/lib/python3.11/dist-packages (from peft->nemo_toolkit->nemo_toolkit[all]) (1.5.2)\nRequirement already satisfied: sortedcontainers>=2.0.4 in /usr/local/lib/python3.11/dist-packages (from pyannote.core->nemo_toolkit->nemo_toolkit[all]) (2.4.0)\nCollecting pyannote.database>=4.0.1 (from pyannote.metrics->nemo_toolkit->nemo_toolkit[all])\n  Downloading pyannote.database-5.1.3-py3-none-any.whl.metadata (1.1 kB)\nRequirement already satisfied: future>=0.16.0 in /usr/local/lib/python3.11/dist-packages (from pyloudnorm->nemo_toolkit->nemo_toolkit[all]) (1.0.0)\nRequirement already satisfied: iniconfig in /usr/local/lib/python3.11/dist-packages (from pytest->nemo_toolkit->nemo_toolkit[all]) (2.1.0)\nRequirement already satisfied: pluggy<2,>=1.5 in /usr/local/lib/python3.11/dist-packages (from pytest->nemo_toolkit->nemo_toolkit[all]) (1.5.0)\nRequirement already satisfied: Werkzeug>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from pytest-httpserver->nemo_toolkit->nemo_toolkit[all]) (3.1.3)\nCollecting av (from qwen_vl_utils->nemo_toolkit->nemo_toolkit[all])\n  Downloading av-14.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.6 kB)\nCollecting ruamel.yaml.clib>=0.2.7 (from ruamel.yaml->nemo_toolkit->nemo_toolkit[all])\n  Downloading ruamel.yaml.clib-0.2.12-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.7 kB)\nRequirement already satisfied: sphinxcontrib-applehelp>=1.0.7 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.0.0)\nRequirement already satisfied: sphinxcontrib-devhelp>=1.0.6 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.0.0)\nRequirement already satisfied: sphinxcontrib-htmlhelp>=2.0.6 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.1.0)\nRequirement already satisfied: sphinxcontrib-jsmath>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (1.0.1)\nRequirement already satisfied: sphinxcontrib-qthelp>=1.0.6 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.0.0)\nRequirement already satisfied: sphinxcontrib-serializinghtml>=1.1.9 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.0.0)\nRequirement already satisfied: Pygments>=2.17 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.19.1)\nRequirement already satisfied: docutils<0.22,>=0.20 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (0.21.2)\nRequirement already satisfied: snowballstemmer>=2.2 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.2.0)\nRequirement already satisfied: babel>=2.13 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (2.17.0)\nRequirement already satisfied: alabaster>=0.7.14 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (1.0.0)\nRequirement already satisfied: imagesize>=1.3 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (1.4.1)\nRequirement already satisfied: roman-numerals-py>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from sphinx->nemo_toolkit->nemo_toolkit[all]) (3.1.0)\nCollecting pybtex>=0.24 (from sphinxcontrib-bibtex->nemo_toolkit->nemo_toolkit[all])\n  Downloading pybtex-0.24.0-py2.py3-none-any.whl.metadata (2.0 kB)\nCollecting pybtex-docutils>=1.0.0 (from sphinxcontrib-bibtex->nemo_toolkit->nemo_toolkit[all])\n  Downloading pybtex_docutils-1.0.3-py3-none-any.whl.metadata (4.3 kB)\nRequirement already satisfied: grpcio>=1.48.2 in /usr/local/lib/python3.11/dist-packages (from tensorboard->nemo_toolkit->nemo_toolkit[all]) (1.72.0rc1)\nRequirement already satisfied: markdown>=2.6.8 in /usr/local/lib/python3.11/dist-packages (from tensorboard->nemo_toolkit->nemo_toolkit[all]) (3.7)\nRequirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /usr/local/lib/python3.11/dist-packages (from tensorboard->nemo_toolkit->nemo_toolkit[all]) (0.7.2)\nCollecting trampoline>=0.1.2 (from torchsde->nemo_toolkit->nemo_toolkit[all])\n  Downloading trampoline-0.1.2-py3-none-any.whl.metadata (10 kB)\nRequirement already satisfied: docker-pycreds>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from wandb->nemo_toolkit->nemo_toolkit[all]) (0.4.0)\nRequirement already satisfied: gitpython!=3.1.29,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from wandb->nemo_toolkit->nemo_toolkit[all]) (3.1.44)\nRequirement already satisfied: sentry-sdk>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from wandb->nemo_toolkit->nemo_toolkit[all]) (2.25.1)\nRequirement already satisfied: setproctitle in /usr/local/lib/python3.11/dist-packages (from wandb->nemo_toolkit->nemo_toolkit[all]) (1.3.5)\nCollecting indic-numtowords (from whisper_normalizer->nemo_toolkit->nemo_toolkit[all])\n  Downloading indic_numtowords-1.0.2.tar.gz (24 kB)\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: Mako in /usr/local/lib/python3.11/dist-packages (from alembic>=1.5.0->optuna->nemo_toolkit->nemo_toolkit[all]) (1.3.10)\nRequirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /usr/local/lib/python3.11/dist-packages (from botocore<1.39.0,>=1.38.11->boto3->nemo_toolkit->nemo_toolkit[all]) (2.4.0)\nRequirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.0->soundfile->nemo_toolkit->nemo_toolkit[all]) (2.22)\nRequirement already satisfied: toolz>=0.8.0 in /usr/local/lib/python3.11/dist-packages (from cytoolz>=0.10.1->lhotse!=1.31.0->nemo_toolkit->nemo_toolkit[all]) (1.0.0)\nCollecting invoke>=2.0 (from fabric>=3.2.2->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading invoke-2.2.0-py3-none-any.whl.metadata (3.3 kB)\nCollecting paramiko>=2.4 (from fabric>=3.2.2->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading paramiko-3.5.1-py3-none-any.whl.metadata (4.6 kB)\nCollecting decorator>=4.3.0 (from librosa>=0.10.1->nemo_toolkit->nemo_toolkit[all])\n  Downloading decorator-5.2.1-py3-none-any.whl.metadata (3.9 kB)\nRequirement already satisfied: deprecated>=1.2 in /usr/local/lib/python3.11/dist-packages (from fabric>=3.2.2->nemo_run->nemo_toolkit->nemo_toolkit[all]) (1.2.18)\nRequirement already satisfied: itsdangerous>=2.2 in /usr/local/lib/python3.11/dist-packages (from Flask>=0.8->flask_restful->nemo_toolkit->nemo_toolkit[all]) (2.2.0)\nRequirement already satisfied: blinker>=1.9 in /usr/local/lib/python3.11/dist-packages (from Flask>=0.8->flask_restful->nemo_toolkit->nemo_toolkit[all]) (1.9.0)\nRequirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (2.6.1)\nRequirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.3.2)\nRequirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (25.3.0)\nRequirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.6.0)\nRequirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (6.4.3)\nRequirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (0.3.1)\nRequirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all]) (1.20.0)\nRequirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython!=3.1.29,>=1.0.0->wandb->nemo_toolkit->nemo_toolkit[all]) (4.0.12)\nCollecting pfzy<0.4.0,>=0.3.1 (from inquirerpy>=0.3.4->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading pfzy-0.3.4-py3-none-any.whl.metadata (4.9 kB)\nRequirement already satisfied: prompt-toolkit<4.0.0,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from inquirerpy>=0.3.4->nemo_run->nemo_toolkit->nemo_toolkit[all]) (3.0.50)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->nemo_toolkit->nemo_toolkit[all]) (3.0.2)\nRequirement already satisfied: jsonschema<5.0,>=4.0 in /usr/local/lib/python3.11/dist-packages (from multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (4.23.0)\nRequirement already satisfied: opentelemetry-api<2.0,>=1.24 in /usr/local/lib/python3.11/dist-packages (from multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (1.31.1)\nCollecting wcmatch<11.0,>=10.0 (from multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading wcmatch-10.0-py3-none-any.whl.metadata (5.0 kB)\nCollecting xattr<1.2.0,>=1.1.4 (from multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading xattr-1.1.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\nCollecting latexcodec>=1.0.4 (from pybtex>=0.24->sphinxcontrib-bibtex->nemo_toolkit->nemo_toolkit[all])\n  Downloading latexcodec-3.0.0-py3-none-any.whl.metadata (4.9 kB)\nRequirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (0.7.0)\nRequirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (2.33.2)\nRequirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->nvidia-modelopt<=0.29.0,>=0.27.0->nvidia-modelopt[torch]<=0.29.0,>=0.27.0; platform_system != \"Darwin\" and extra == \"all\"->nemo_toolkit->nemo_toolkit[all]) (0.4.0)\nRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->tiktoken==0.7.0->nemo_toolkit->nemo_toolkit[all]) (3.4.2)\nRequirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=12->nerfacc>=0.5.3->nemo_toolkit->nemo_toolkit[all]) (3.0.0)\nRequirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy>=1.4.2->optuna->nemo_toolkit->nemo_toolkit[all]) (3.1.1)\nCollecting pyre-extensions (from torchx>=0.7.0->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading pyre_extensions-0.0.32-py3-none-any.whl.metadata (4.0 kB)\nRequirement already satisfied: docstring-parser>=0.8.1 in /usr/local/lib/python3.11/dist-packages (from torchx>=0.7.0->nemo_run->nemo_toolkit->nemo_toolkit[all]) (0.16)\nRequirement already satisfied: docker in /usr/local/lib/python3.11/dist-packages (from torchx>=0.7.0->nemo_run->nemo_toolkit->nemo_toolkit[all]) (7.1.0)\nCollecting urllib3!=2.2.0,<3,>=1.25.4 (from botocore<1.39.0,>=1.38.11->boto3->nemo_toolkit->nemo_toolkit[all])\n  Downloading urllib3-1.26.20-py2.py3-none-any.whl.metadata (50 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.1/50.1 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hRequirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer>=0.12.3->nemo_run->nemo_toolkit->nemo_toolkit[all]) (1.5.4)\nRequirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from dataclasses-json->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (0.9.0)\nRequirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib-metadata->diffusers>=0.19.3->nemo_toolkit->nemo_toolkit[all]) (3.21.0)\nRequirement already satisfied: jedi>=0.16 in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.19.2)\nRequirement already satisfied: pickleshare in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.7.5)\nRequirement already satisfied: traitlets>=4.2 in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (5.7.1)\nRequirement already satisfied: backcall in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.2.0)\nRequirement already satisfied: matplotlib-inline in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.1.7)\nRequirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.11/dist-packages (from ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (4.9.0)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2022.1.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (1.3.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2024.2.0)\nCollecting DataProperty<2,>=1.1.0 (from pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading DataProperty-1.1.0-py3-none-any.whl.metadata (11 kB)\nCollecting mbstrdecoder<2,>=1.0.0 (from pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading mbstrdecoder-1.1.4-py3-none-any.whl.metadata (4.3 kB)\nCollecting pathvalidate<4,>=2.3.0 (from pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading pathvalidate-3.2.3-py3-none-any.whl.metadata (12 kB)\nCollecting tabledata<2,>=1.3.1 (from pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading tabledata-1.3.4-py3-none-any.whl.metadata (3.7 kB)\nCollecting tcolorpy<1,>=0.0.5 (from pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading tcolorpy-0.1.7-py3-none-any.whl.metadata (6.3 kB)\nCollecting typepy<2,>=1.3.2 (from typepy[datetime]<2,>=1.3.2->pytablewriter->nvidia-lm-eval->nemo_toolkit->nemo_toolkit[all])\n  Downloading typepy-1.3.4-py3-none-any.whl.metadata (9.2 kB)\nRequirement already satisfied: PySocks!=1.5.7,>=1.5.6 in /usr/local/lib/python3.11/dist-packages (from requests[socks]->gdown->nemo_toolkit->nemo_toolkit[all]) (1.7.1)\nCollecting aiofiles>=24.1.0 (from unstructured-client->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all])\n  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\nRequirement already satisfied: nest-asyncio>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from unstructured-client->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (1.6.0)\nRequirement already satisfied: pypdf>=4.0 in /usr/local/lib/python3.11/dist-packages (from unstructured-client->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (5.4.0)\nRequirement already satisfied: requests-toolbelt>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from unstructured-client->unstructured==0.14.9->nemo_toolkit->nemo_toolkit[all]) (1.0.0)\nRequirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.29,>=1.0.0->wandb->nemo_toolkit->nemo_toolkit[all]) (5.0.2)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.22->nemo_toolkit->nemo_toolkit[all]) (2024.2.0)\nRequirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.11/dist-packages (from jedi>=0.16->ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.8.4)\nRequirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0,>=4.0->multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (2024.10.1)\nRequirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0,>=4.0->multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (0.36.2)\nRequirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema<5.0,>=4.0->multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all]) (0.24.0)\nRequirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=12->nerfacc>=0.5.3->nemo_toolkit->nemo_toolkit[all]) (0.1.2)\nCollecting importlib-metadata (from diffusers>=0.19.3->nemo_toolkit->nemo_toolkit[all])\n  Downloading importlib_metadata-8.6.1-py3-none-any.whl.metadata (4.7 kB)\nCollecting bcrypt>=3.2 (from paramiko>=2.4->fabric>=3.2.2->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading bcrypt-4.3.0-cp39-abi3-manylinux_2_34_x86_64.whl.metadata (10 kB)\nCollecting pynacl>=1.5 (from paramiko>=2.4->fabric>=3.2.2->nemo_run->nemo_toolkit->nemo_toolkit[all])\n  Downloading PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl.metadata (8.6 kB)\nRequirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.11/dist-packages (from pexpect>4.3->ipython->mediapy==1.1.6->nemo_toolkit->nemo_toolkit[all]) (0.7.0)\nCollecting bracex>=2.1.1 (from wcmatch<11.0,>=10.0->multi-storage-client>=0.13.0->megatron-energon==5.2.0->nemo_toolkit->nemo_toolkit[all])\n  Downloading bracex-2.5.post1-py3-none-any.whl.metadata (3.5 kB)\nDownloading fsspec-2024.12.0-py3-none-any.whl (183 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m183.9/183.9 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading bitsandbytes-0.45.3-py3-none-manylinux_2_24_x86_64.whl (76.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.1/76.1 MB\u001b[0m \u001b[31m23.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading mediapy-1.1.6-py3-none-any.whl (24 kB)\nDownloading megatron_energon-5.2.0-py3-none-any.whl (175 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m175.9/175.9 kB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading open_clip_torch-2.24.0-py3-none-any.whl (1.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.5/1.5 MB\u001b[0m \u001b[31m50.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading tiktoken-0.7.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m47.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading unstructured-0.14.9-py3-none-any.whl (2.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.1/2.1 MB\u001b[0m \u001b[31m57.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading black-24.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl (1.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m59.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading hydra_core-1.3.2-py3-none-any.whl (154 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m154.5/154.5 kB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading isort-5.13.2-py3-none-any.whl (92 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.3/92.3 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading jiwer-3.1.0-py3-none-any.whl (22 kB)\nDownloading lhotse-1.30.3-py3-none-any.whl (851 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m851.4/851.4 kB\u001b[0m \u001b[31m37.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading lightning-2.4.0-py3-none-any.whl (810 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m811.0/811.0 kB\u001b[0m \u001b[31m35.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nerfacc-0.5.3-py3-none-any.whl (54 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.6/54.6 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_modelopt-0.29.0-py3-none-manylinux_2_28_x86_64.whl (690 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m690.1/690.1 kB\u001b[0m \u001b[31m34.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_modelopt_core-0.29.0-cp311-cp311-manylinux_2_28_x86_64.whl (1.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m50.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_resiliency_ext-0.4.0-cp311-cp311-manylinux_2_31_x86_64.whl (441 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m441.4/441.4 kB\u001b[0m \u001b[31m23.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading packaging-24.2-py3-none-any.whl (65 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.5/65.5 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading protobuf-4.24.4-cp37-abi3-manylinux2014_x86_64.whl (311 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m311.6/311.6 kB\u001b[0m \u001b[31m15.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading rapidfuzz-3.13.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m79.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading sacremoses-0.1.1-py3-none-any.whl (897 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.5/897.5 kB\u001b[0m \u001b[31m38.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading tensorstore-0.1.71-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (17.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.8/17.8 MB\u001b[0m \u001b[31m85.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading textdistance-4.6.3-py3-none-any.whl (31 kB)\nDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m31.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m13.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m79.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading webdataset-0.2.111-py3-none-any.whl (85 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.5/85.5 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading zarr-2.18.7-py3-none-any.whl (211 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.3/211.3 kB\u001b[0m \u001b[31m12.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading accelerated_scan-0.2.0-py3-none-any.whl (11 kB)\nDownloading addict-2.4.0-py3-none-any.whl (3.8 kB)\nDownloading attrdict-2.0.1-py2.py3-none-any.whl (9.9 kB)\nDownloading braceexpand-0.1.7-py2.py3-none-any.whl (5.9 kB)\nDownloading decord-0.6.0-py3-none-manylinux2010_x86_64.whl (13.6 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.6/13.6 MB\u001b[0m \u001b[31m94.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading einops_exts-0.0.4-py3-none-any.whl (3.9 kB)\nDownloading faiss_cpu-1.11.0-cp311-cp311-manylinux_2_28_x86_64.whl (31.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m31.3/31.3 MB\u001b[0m \u001b[31m58.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading fiddle-0.3.0-py3-none-any.whl (419 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m419.8/419.8 kB\u001b[0m \u001b[31m20.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading Flask_RESTful-0.3.10-py2.py3-none-any.whl (26 kB)\nDownloading ftfy-6.3.1-py3-none-any.whl (44 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.8/44.8 kB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading g2p_en-2.1.0-py3-none-any.whl (3.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m80.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading ijson-3.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (134 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m135.0/135.0 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading kaldiio-2.18.1-py3-none-any.whl (29 kB)\nDownloading markdown2-2.5.3-py3-none-any.whl (48 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.5/48.5 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading megatron_core-0.12.1-cp311-cp311-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl (1.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m56.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nemo_run-0.4.0-py3-none-any.whl (174 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m174.4/174.4 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nemo_text_processing-1.1.0-py3-none-any.whl (2.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.7/2.7 MB\u001b[0m \u001b[31m67.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading pynini-2.1.6.post1-cp311-cp311-manylinux_2_28_x86_64.whl (154.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m154.8/154.8 MB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading num2words-0.5.14-py3-none-any.whl (163 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m163.5/163.5 kB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_lm_eval-25.4.1-py3-none-any.whl (4.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.2/4.2 MB\u001b[0m \u001b[31m59.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading beautifulsoup4-4.13.1-py3-none-any.whl (185 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m185.1/185.1 kB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading httpx-0.27.0-py3-none-any.whl (75 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.6/75.6 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading immutabledict-4.2.0-py3-none-any.whl (4.7 kB)\nDownloading openai-1.61.0-py3-none-any.whl (460 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m460.6/460.6 kB\u001b[0m \u001b[31m24.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading sacrebleu-2.5.1-py3-none-any.whl (104 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m104.1/104.1 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading OpenCC-1.1.9-cp311-cp311-manylinux2014_x86_64.whl (1.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m59.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pangu-*******-py3-none-any.whl (6.4 kB)\nDownloading parameterized-0.9.0-py2.py3-none-any.whl (20 kB)\nDownloading pyannote.core-5.0.0-py3-none-any.whl (58 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.5/58.5 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pyannote.metrics-3.2.1-py3-none-any.whl (51 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.4/51.4 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pyloudnorm-0.1.1-py3-none-any.whl (9.6 kB)\nDownloading pypinyin-0.54.0-py2.py3-none-any.whl (837 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m837.0/837.0 kB\u001b[0m \u001b[31m40.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pypinyin_dict-0.9.0-py2.py3-none-any.whl (9.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m9.5/9.5 MB\u001b[0m \u001b[31m104.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading pystoi-0.4.1-py2.py3-none-any.whl (8.2 kB)\nDownloading pytest_httpserver-1.1.3-py3-none-any.whl (21 kB)\nDownloading pytest_mock-3.14.1-py3-none-any.whl (9.9 kB)\nUsing cached pytest_runner-6.0.1-py3-none-any.whl (7.2 kB)\nDownloading qwen_vl_utils-0.0.11-py3-none-any.whl (7.6 kB)\nDownloading resampy-0.4.3-py3-none-any.whl (3.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.1/3.1 MB\u001b[0m \u001b[31m78.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading ruamel.yaml-0.18.12-py3-none-any.whl (118 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m118.4/118.4 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading sphinxcontrib_bibtex-2.6.3-py3-none-any.whl (40 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.3/40.3 kB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading taming_transformers-0.0.1-py3-none-any.whl (45 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.6/45.6 kB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading torchdiffeq-0.2.5-py3-none-any.whl (32 kB)\nDownloading torchsde-0.2.6-py3-none-any.whl (61 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.2/61.2 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading trimesh-4.6.10-py3-none-any.whl (711 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m711.2/711.2 kB\u001b[0m \u001b[31m35.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading whisper_normalizer-0.1.11-py3-none-any.whl (36 kB)\nDownloading aniso8601-10.0.1-py2.py3-none-any.whl (52 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.8/52.8 kB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading cryptography-42.0.8-cp39-abi3-manylinux_2_28_x86_64.whl (3.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.9/3.9 MB\u001b[0m \u001b[31m84.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading evaluate-0.4.3-py3-none-any.whl (84 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m84.0/84.0 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading fabric-3.2.2-py3-none-any.whl (59 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.4/59.4 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading decorator-5.2.1-py3-none-any.whl (9.2 kB)\nDownloading InquirerPy-0.3.4-py3-none-any.whl (67 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.7/67.7 kB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading lilcom-1.8.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (94 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m94.3/94.3 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading multi_storage_client-0.21.0-py3-none-any.whl (169 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.4/169.4 kB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading numcodecs-0.15.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (8.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m108.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading pathspec-0.12.1-py3-none-any.whl (31 kB)\nDownloading pyannote.database-5.1.3-py3-none-any.whl (48 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m48.1/48.1 kB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pybtex-0.24.0-py2.py3-none-any.whl (561 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m561.4/561.4 kB\u001b[0m \u001b[31m32.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pybtex_docutils-1.0.3-py3-none-any.whl (6.4 kB)\nDownloading ruamel.yaml.clib-0.2.12-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (739 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m739.1/739.1 kB\u001b[0m \u001b[31m39.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading torchprofile-0.0.4-py3-none-any.whl (7.7 kB)\nDownloading torchx-0.7.0-py3-none-any.whl (256 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m256.1/256.1 kB\u001b[0m \u001b[31m15.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading trampoline-0.1.2-py3-none-any.whl (5.2 kB)\nDownloading av-14.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (35.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m35.3/35.3 MB\u001b[0m \u001b[31m51.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading backoff-2.2.1-py3-none-any.whl (15 kB)\nDownloading fasteners-0.19-py3-none-any.whl (18 kB)\nDownloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\nDownloading jsonlines-4.0.0-py3-none-any.whl (8.7 kB)\nDownloading levenshtein-0.27.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (161 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m161.7/161.7 kB\u001b[0m \u001b[31m11.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading libcst-1.8.0-cp311-cp311-manylinux_2_28_x86_64.whl (2.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m69.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading loguru-0.7.3-py3-none-any.whl (61 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.6/61.6 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading plac-1.4.5-py2.py3-none-any.whl (22 kB)\nDownloading portalocker-3.1.1-py3-none-any.whl (19 kB)\nDownloading pulp-3.2.1-py3-none-any.whl (16.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.4/16.4 MB\u001b[0m \u001b[31m88.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading pytablewriter-1.2.1-py3-none-any.whl (91 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m91.1/91.1 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pytest_cov-6.1.1-py3-none-any.whl (23 kB)\nDownloading pytest_random_order-1.1.1-py3-none-any.whl (11 kB)\nDownloading python_iso639-2025.2.18-py3-none-any.whl (167 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m167.6/167.6 kB\u001b[0m \u001b[31m9.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\nDownloading tqdm_multiprocess-0.0.11-py3-none-any.whl (9.8 kB)\nDownloading unstructured_client-0.36.0-py3-none-any.whl (195 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m195.8/195.8 kB\u001b[0m \u001b[31m12.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading aiofiles-24.1.0-py3-none-any.whl (15 kB)\nDownloading DataProperty-1.1.0-py3-none-any.whl (27 kB)\nDownloading invoke-2.2.0-py3-none-any.whl (160 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m160.3/160.3 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading latexcodec-3.0.0-py3-none-any.whl (18 kB)\nDownloading mbstrdecoder-1.1.4-py3-none-any.whl (7.9 kB)\nDownloading importlib_metadata-8.6.1-py3-none-any.whl (26 kB)\nDownloading paramiko-3.5.1-py3-none-any.whl (227 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.3/227.3 kB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pathvalidate-3.2.3-py3-none-any.whl (24 kB)\nDownloading pfzy-0.3.4-py3-none-any.whl (8.5 kB)\nDownloading tabledata-1.3.4-py3-none-any.whl (11 kB)\nDownloading tcolorpy-0.1.7-py3-none-any.whl (8.1 kB)\nDownloading typepy-1.3.4-py3-none-any.whl (31 kB)\nDownloading urllib3-1.26.20-py2.py3-none-any.whl (144 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m144.2/144.2 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading wcmatch-10.0-py3-none-any.whl (39 kB)\nDownloading xattr-1.1.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (38 kB)\nDownloading pyre_extensions-0.0.32-py3-none-any.whl (12 kB)\nDownloading bcrypt-4.3.0-cp39-abi3-manylinux_2_34_x86_64.whl (284 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m284.2/284.2 kB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading bracex-2.5.post1-py3-none-any.whl (11 kB)\nDownloading PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl (856 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m856.7/856.7 kB\u001b[0m \u001b[31m45.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hBuilding wheels for collected packages: nemo_toolkit, cdifflib, progress, sox, texterrors, clip, kaldi-python-io, langdetect, rouge_score, pesq, distance, docopt, intervaltree, asciitree, indic-numtowords, word2number\n  Building wheel for nemo_toolkit (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for nemo_toolkit: filename=nemo_toolkit-2.4.0rc0-py3-none-any.whl size=7047712 sha256=730622b56eca918b5fc3ecc47d7dde10cfdc9060f1fa76a8b0a652fb1a995421\n  Stored in directory: /tmp/pip-ephem-wheel-cache-e60p2o9h/wheels/d2/b5/a3/ab51ef7d1a3339f412970ddbc5f6a6f2136dd2116423c3a3af\n  Building wheel for cdifflib (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for cdifflib: filename=cdifflib-1.2.6-cp311-cp311-linux_x86_64.whl size=28740 sha256=2f1aa7c7e5048d0963ce6c8170c37ca4897a66d6219eed047a422eb6862304c1\n  Stored in directory: /root/.cache/pip/wheels/61/25/f2/4ee06fe9d0bcb43991be6302633001497862ff216060e9361f\n  Building wheel for progress (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for progress: filename=progress-1.6-py3-none-any.whl size=9613 sha256=55098fc60971e84e6c6da418ceecbd56d32dda62fa7f60667fa2541efc2802e6\n  Stored in directory: /root/.cache/pip/wheels/b5/b9/86/f1bcc2a1de592673c4192d9459c0da1100d70212f38b6bd2a4\n  Building wheel for sox (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for sox: filename=sox-1.5.0-py3-none-any.whl size=40036 sha256=438e646791d0739a50914f0c3f74afc5b0425f98cfa998b784fa9b307f6c2349\n  Stored in directory: /root/.cache/pip/wheels/74/89/93/023fcdacaec4e5471e78b43992515e8500cc2505b307e2e6b7\n  Building wheel for texterrors (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for texterrors: filename=texterrors-0.5.1-cp311-cp311-linux_x86_64.whl size=1078000 sha256=1c8a5d9f026841d06fc33b46e4286de03fdee8985e01a7eaefcda3b1922b37a9\n  Stored in directory: /root/.cache/pip/wheels/6f/94/c8/7edaa578fc800d26e3fda18fba557a4218ab553d078ee51b46\n  Building wheel for clip (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for clip: filename=clip-0.2.0-py3-none-any.whl size=6989 sha256=24184252675e7b5838eedfda190d24a15de462b563f6fbd81a9f1c797bb19511\n  Stored in directory: /root/.cache/pip/wheels/ab/a5/e8/c9fa20742edbccf2702dae8ee62053e6c460e961d45967b49c\n  Building wheel for kaldi-python-io (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for kaldi-python-io: filename=kaldi_python_io-1.2.2-py3-none-any.whl size=8953 sha256=4e46501be468aba67cda3d45ff9319739122123b325681c15786f97d2f1712a5\n  Stored in directory: /root/.cache/pip/wheels/f2/86/7b/eec1bb7dc63b8aab5da6317609313873e6e75f065b65f3c29c\n  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993223 sha256=dc18b6fb0a090f21a8552cca986116c46935d545b60f063494f50c5237e7f0d4\n  Stored in directory: /root/.cache/pip/wheels/0a/f2/b2/e5ca405801e05eb7c8ed5b3b4bcf1fcabcd6272c167640072e\n  Building wheel for rouge_score (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for rouge_score: filename=rouge_score-0.1.2-py3-none-any.whl size=24934 sha256=629c85d8bf5ca0564ba40302b58631289f0c5d4472a7071b757517bb7dd26947\n  Stored in directory: /root/.cache/pip/wheels/1e/19/43/8a442dc83660ca25e163e1bd1f89919284ab0d0c1475475148\n  Building wheel for pesq (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for pesq: filename=pesq-0.0.4-cp311-cp311-linux_x86_64.whl size=275939 sha256=abd65596c8f39a0b96ff68ddd82d15e6101705118ebdf9f2ce79179d73bfbfdb\n  Stored in directory: /root/.cache/pip/wheels/ae/f1/23/2698d0bf31eec2b2aa50623b5d93b6206c49c7155d0e31345d\n  Building wheel for distance (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for distance: filename=Distance-0.1.3-py3-none-any.whl size=16256 sha256=854d54405cf56c0020db78253a4bc54e9456bcf31ada60558ad43bb4a6b8b713\n  Stored in directory: /root/.cache/pip/wheels/fb/cd/9c/3ab5d666e3bcacc58900b10959edd3816cc9557c7337986322\n  Building wheel for docopt (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for docopt: filename=docopt-0.6.2-py2.py3-none-any.whl size=13706 sha256=ef25938c05e597c97cf00fadea6c66f09eb00418414185545644a3c01e4e6338\n  Stored in directory: /root/.cache/pip/wheels/1a/b0/8c/4b75c4116c31f83c8f9f047231251e13cc74481cca4a78a9ce\n  Building wheel for intervaltree (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for intervaltree: filename=intervaltree-3.1.0-py2.py3-none-any.whl size=26098 sha256=fa3962668803653fb9ac46518b274c210f4d4c1281c97420255f21d3fbe18803\n  Stored in directory: /root/.cache/pip/wheels/31/d7/d9/eec6891f78cac19a693bd40ecb8365d2f4613318c145ec9816\n  Building wheel for asciitree (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for asciitree: filename=asciitree-0.3.3-py3-none-any.whl size=5031 sha256=8213add5f601556acdec358fe9da55a25ecb661b5039e8f9ae2dbe69f763d6ba\n  Stored in directory: /root/.cache/pip/wheels/71/c1/da/23077eb3b87d24d6f3852ed1ed1a1ac2d3c885ad6ebd2b4a07\n  Building wheel for indic-numtowords (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for indic-numtowords: filename=indic_numtowords-1.0.2-py3-none-any.whl size=39214 sha256=31616ace50370c0ef673215dbcd2704353a0912db42ed41cbf25f64b8ed3a768\n  Stored in directory: /root/.cache/pip/wheels/09/b1/af/e78074d6002f3805735ab2eb3c0c42222b68db37e7face6b25\n  Building wheel for word2number (setup.py) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for word2number: filename=word2number-1.1-py3-none-any.whl size=5568 sha256=7dd43b2b48de8ae19e19f90f7a053fe9a2c32ba91fc4af87e4bf0d103c43b62a\n  Stored in directory: /root/.cache/pip/wheels/cd/ef/ae/073b491b14d25e2efafcffca9e16b2ee6d114ec5c643ba4f06\nSuccessfully built nemo_toolkit cdifflib progress sox texterrors clip kaldi-python-io langdetect rouge_score pesq distance docopt intervaltree asciitree indic-numtowords word2number\nInstalling collected packages: word2number, trampoline, progress, plac, pesq, pangu, opencc, filetype, docopt, distance, clip, braceexpand, asciitree, aniso8601, addict, urllib3, tqdm-multiprocess, textdistance, tcolorpy, sacremoses, ruamel.yaml.clib, rapidfuzz, python-magic, python-iso639, pytest-runner, pypinyin, pynini, pulp, protobuf, portalocker, pfzy, pathvalidate, pathspec, parameterized, packaging, nvidia-nvjitlink-cu12, nvidia-modelopt-core, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cublas-cu12, num2words, mbstrdecoder, markdown2, loguru, libcst, latexcodec, langdetect, jsonlines, isort, invoke, intervaltree, indic-numtowords, importlib-metadata, immutabledict, ijson, ftfy, fsspec, fasteners, einops_exts, decorator, cdifflib, bracex, beautifulsoup4, bcrypt, backoff, av, attrdict, aiofiles, xattr, whisper_normalizer, wcmatch, typepy, ruamel.yaml, pytest-httpserver, pyre-extensions, pypinyin-dict, pynacl, pybtex, nvidia-cusparse-cu12, nvidia-cudnn-cu12, Levenshtein, jiwer, inquirerpy, hydra-core, httpx, fiddle, cryptography, black, tiktoken, qwen_vl_utils, pytest-random-order, pytest-mock, pytest-cov, pybtex-docutils, paramiko, openai, nvidia-cusolver-cu12, flask_restful, unstructured-client, torchx, sphinxcontrib-bibtex, multi-storage-client, fabric, DataProperty, tabledata, nvidia-resiliency-ext, nerfacc, nemo_run, accelerated-scan, pytablewriter, torchprofile, pyannote.core, nvidia-modelopt, numcodecs, zarr, webdataset, tensorstore, sacrebleu, rouge_score, pyannote.database, lilcom, evaluate, unstructured, trimesh, torchsde, torchdiffeq, texterrors, taming-transformers, sox, resampy, pystoi, pyloudnorm, pyannote.metrics, open_clip_torch, nvidia-lm-eval, nemo_toolkit, nemo_text_processing, megatron-energon, megatron_core, mediapy, lightning, lhotse, kaldiio, kaldi-python-io, g2p_en, faiss-cpu, decord, bitsandbytes\n  Attempting uninstall: urllib3\n    Found existing installation: urllib3 2.4.0\n    Uninstalling urllib3-2.4.0:\n      Successfully uninstalled urllib3-2.4.0\n  Attempting uninstall: protobuf\n    Found existing installation: protobuf 3.20.3\n    Uninstalling protobuf-3.20.3:\n      Successfully uninstalled protobuf-3.20.3\n  Attempting uninstall: packaging\n    Found existing installation: packaging 25.0\n    Uninstalling packaging-25.0:\n      Successfully uninstalled packaging-25.0\n  Attempting uninstall: nvidia-nvjitlink-cu12\n    Found existing installation: nvidia-nvjitlink-cu12 12.9.41\n    Uninstalling nvidia-nvjitlink-cu12-12.9.41:\n      Successfully uninstalled nvidia-nvjitlink-cu12-12.9.41\n  Attempting uninstall: nvidia-curand-cu12\n    Found existing installation: nvidia-curand-cu12 10.3.10.19\n    Uninstalling nvidia-curand-cu12-10.3.10.19:\n      Successfully uninstalled nvidia-curand-cu12-10.3.10.19\n  Attempting uninstall: nvidia-cufft-cu12\n    Found existing installation: nvidia-cufft-cu12 11.4.0.6\n    Uninstalling nvidia-cufft-cu12-11.4.0.6:\n      Successfully uninstalled nvidia-cufft-cu12-11.4.0.6\n  Attempting uninstall: nvidia-cublas-cu12\n    Found existing installation: nvidia-cublas-cu12 12.9.0.13\n    Uninstalling nvidia-cublas-cu12-12.9.0.13:\n      Successfully uninstalled nvidia-cublas-cu12-12.9.0.13\n  Attempting uninstall: importlib-metadata\n    Found existing installation: importlib_metadata 8.7.0\n    Uninstalling importlib_metadata-8.7.0:\n      Successfully uninstalled importlib_metadata-8.7.0\n  Attempting uninstall: immutabledict\n    Found existing installation: immutabledict 4.2.1\n    Uninstalling immutabledict-4.2.1:\n      Successfully uninstalled immutabledict-4.2.1\n  Attempting uninstall: fsspec\n    Found existing installation: fsspec 2025.3.2\n    Uninstalling fsspec-2025.3.2:\n      Successfully uninstalled fsspec-2025.3.2\n  Attempting uninstall: decorator\n    Found existing installation: decorator 4.4.2\n    Uninstalling decorator-4.4.2:\n      Successfully uninstalled decorator-4.4.2\n  Attempting uninstall: beautifulsoup4\n    Found existing installation: beautifulsoup4 4.13.3\n    Uninstalling beautifulsoup4-4.13.3:\n      Successfully uninstalled beautifulsoup4-4.13.3\n  Attempting uninstall: aiofiles\n    Found existing installation: aiofiles 22.1.0\n    Uninstalling aiofiles-22.1.0:\n      Successfully uninstalled aiofiles-22.1.0\n  Attempting uninstall: nvidia-cusparse-cu12\n    Found existing installation: nvidia-cusparse-cu12 12.5.9.5\n    Uninstalling nvidia-cusparse-cu12-12.5.9.5:\n      Successfully uninstalled nvidia-cusparse-cu12-12.5.9.5\n  Attempting uninstall: nvidia-cudnn-cu12\n    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n  Attempting uninstall: httpx\n    Found existing installation: httpx 0.28.1\n    Uninstalling httpx-0.28.1:\n      Successfully uninstalled httpx-0.28.1\n  Attempting uninstall: cryptography\n    Found existing installation: cryptography 44.0.3\n    Uninstalling cryptography-44.0.3:\n      Successfully uninstalled cryptography-44.0.3\n  Attempting uninstall: tiktoken\n    Found existing installation: tiktoken 0.9.0\n    Uninstalling tiktoken-0.9.0:\n      Successfully uninstalled tiktoken-0.9.0\n  Attempting uninstall: openai\n    Found existing installation: openai 1.70.0\n    Uninstalling openai-1.70.0:\n      Successfully uninstalled openai-1.70.0\n  Attempting uninstall: nvidia-cusolver-cu12\n    Found existing installation: nvidia-cusolver-cu12 *********\n    Uninstalling nvidia-cusolver-cu12-*********:\n      Successfully uninstalled nvidia-cusolver-cu12-*********\n  Attempting uninstall: tensorstore\n    Found existing installation: tensorstore 0.1.73\n    Uninstalling tensorstore-0.1.73:\n      Successfully uninstalled tensorstore-0.1.73\n\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\nrfc3161-client 1.0.1 requires cryptography<45,>=43, but you have cryptography 42.0.8 which is incompatible.\ngoogle-api-core 1.34.1 requires protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<4.0.0dev,>=3.19.5, but you have protobuf 4.24.4 which is incompatible.\nypy-websocket 0.8.4 requires aiofiles<23,>=22.1.0, but you have aiofiles 24.1.0 which is incompatible.\ncesium 0.12.4 requires numpy<3.0,>=2.0, but you have numpy 1.26.4 which is incompatible.\ngoogle-colab 1.0.0 requires google-auth==2.38.0, but you have google-auth 2.40.1 which is incompatible.\ngoogle-colab 1.0.0 requires notebook==6.5.7, but you have notebook 6.5.4 which is incompatible.\ngoogle-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.2.3 which is incompatible.\nmoviepy 1.0.3 requires decorator<5.0,>=4.0.2, but you have decorator 5.2.1 which is incompatible.\nbigframes 1.42.0 requires rich<14,>=12.4.4, but you have rich 14.0.0 which is incompatible.\ngoogle-spark-connect 0.5.2 requires google-api-core>=2.19.1, but you have google-api-core 1.34.1 which is incompatible.\npydrive2 1.21.3 requires pyOpenSSL<=24.2.1,>=19.1.0, but you have pyopenssl 25.0.0 which is incompatible.\ntensorflow-metadata 1.17.0 requires protobuf<6.0.0,>=4.25.2; python_version >= \"3.11\", but you have protobuf 4.24.4 which is incompatible.\ngcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2024.12.0 which is incompatible.\ngoogle-cloud-bigtable 2.30.0 requires google-api-core[grpc]<3.0.0,>=2.16.0, but you have google-api-core 1.34.1 which is incompatible.\ngoogle-cloud-storage 2.19.0 requires google-api-core<3.0.0dev,>=2.15.0, but you have google-api-core 1.34.1 which is incompatible.\nplotnine 0.14.5 requires matplotlib>=3.8.0, but you have matplotlib 3.7.2 which is incompatible.\ngoogle-genai 1.9.0 requires httpx<1.0.0,>=0.28.1, but you have httpx 0.27.0 which is incompatible.\npandas-gbq 0.28.0 requires google-api-core<3.0.0dev,>=2.10.2, but you have google-api-core 1.34.1 which is incompatible.\nmlxtend 0.23.4 requires scikit-learn>=1.3.1, but you have scikit-learn 1.2.2 which is incompatible.\u001b[0m\u001b[31m\n\u001b[0mSuccessfully installed DataProperty-1.1.0 Levenshtein-0.27.1 accelerated-scan-0.2.0 addict-2.4.0 aiofiles-24.1.0 aniso8601-10.0.1 asciitree-0.3.3 attrdict-2.0.1 av-14.4.0 backoff-2.2.1 bcrypt-4.3.0 beautifulsoup4-4.13.1 bitsandbytes-0.45.3 black-24.10.0 braceexpand-0.1.7 bracex-2.5.post1 cdifflib-1.2.6 clip-0.2.0 cryptography-42.0.8 decorator-5.2.1 decord-0.6.0 distance-0.1.3 docopt-0.6.2 einops_exts-0.0.4 evaluate-0.4.3 fabric-3.2.2 faiss-cpu-1.11.0 fasteners-0.19 fiddle-0.3.0 filetype-1.2.0 flask_restful-0.3.10 fsspec-2024.12.0 ftfy-6.3.1 g2p_en-2.1.0 httpx-0.27.0 hydra-core-1.3.2 ijson-3.4.0 immutabledict-4.2.0 importlib-metadata-8.6.1 indic-numtowords-1.0.2 inquirerpy-0.3.4 intervaltree-3.1.0 invoke-2.2.0 isort-5.13.2 jiwer-3.1.0 jsonlines-4.0.0 kaldi-python-io-1.2.2 kaldiio-2.18.1 langdetect-1.0.9 latexcodec-3.0.0 lhotse-1.30.3 libcst-1.8.0 lightning-2.4.0 lilcom-1.8.1 loguru-0.7.3 markdown2-2.5.3 mbstrdecoder-1.1.4 mediapy-1.1.6 megatron-energon-5.2.0 megatron_core-0.12.1 multi-storage-client-0.21.0 nemo_run-0.4.0 nemo_text_processing-1.1.0 nemo_toolkit-2.4.0rc0 nerfacc-0.5.3 num2words-0.5.14 numcodecs-0.15.1 nvidia-cublas-cu12-******** nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-lm-eval-25.4.1 nvidia-modelopt-0.29.0 nvidia-modelopt-core-0.29.0 nvidia-nvjitlink-cu12-12.4.127 nvidia-resiliency-ext-0.4.0 open_clip_torch-2.24.0 openai-1.61.0 opencc-1.1.9 packaging-24.2 pangu-******* parameterized-0.9.0 paramiko-3.5.1 pathspec-0.12.1 pathvalidate-3.2.3 pesq-0.0.4 pfzy-0.3.4 plac-1.4.5 portalocker-3.1.1 progress-1.6 protobuf-4.24.4 pulp-3.2.1 pyannote.core-5.0.0 pyannote.database-5.1.3 pyannote.metrics-3.2.1 pybtex-0.24.0 pybtex-docutils-1.0.3 pyloudnorm-0.1.1 pynacl-1.5.0 pynini-2.1.6.post1 pypinyin-0.54.0 pypinyin-dict-0.9.0 pyre-extensions-0.0.32 pystoi-0.4.1 pytablewriter-1.2.1 pytest-cov-6.1.1 pytest-httpserver-1.1.3 pytest-mock-3.14.1 pytest-random-order-1.1.1 pytest-runner-6.0.1 python-iso639-2025.2.18 python-magic-0.4.27 qwen_vl_utils-0.0.11 rapidfuzz-3.13.0 resampy-0.4.3 rouge_score-0.1.2 ruamel.yaml-0.18.12 ruamel.yaml.clib-0.2.12 sacrebleu-2.5.1 sacremoses-0.1.1 sox-1.5.0 sphinxcontrib-bibtex-2.6.3 tabledata-1.3.4 taming-transformers-0.0.1 tcolorpy-0.1.7 tensorstore-0.1.71 textdistance-4.6.3 texterrors-0.5.1 tiktoken-0.7.0 torchdiffeq-0.2.5 torchprofile-0.0.4 torchsde-0.2.6 torchx-0.7.0 tqdm-multiprocess-0.0.11 trampoline-0.1.2 trimesh-4.6.10 typepy-1.3.4 unstructured-0.14.9 unstructured-client-0.36.0 urllib3-1.26.20 wcmatch-10.0 webdataset-0.2.111 whisper_normalizer-0.1.11 word2number-1.1 xattr-1.1.4 zarr-2.18.7\n", "output_type": "stream"}], "execution_count": 1}, {"cell_type": "code", "source": "## Please re run the session after executing the cell above this.", "metadata": {"id": "QYu8_h8kTYbV"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "## Prepare the manifests files.", "metadata": {"id": "Fj71yEzQTuFS"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "! cp <PERSON>_<PERSON><PERSON><PERSON>_Finetune/prepare_manifest.py .\n! python prepare_manifest.py", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JGwVzy5aSK87", "outputId": "32d344ab-f5b4-496e-c876-d51d0ac8ca87", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T12:03:28.730633Z", "iopub.execute_input": "2025-06-02T12:03:28.730888Z", "iopub.status.idle": "2025-06-02T12:18:49.690111Z", "shell.execute_reply.started": "2025-06-02T12:03:28.730854Z", "shell.execute_reply": "2025-06-02T12:18:49.689201Z"}}, "outputs": [{"name": "stdout", "text": "Downloading train dataset from https://www.openslr.org/resources/118/GV_Train_100h.tar.gz...\n100% [................................................] 2039605874 / 2039605874\nExtracting train dataset...\nTrain dataset extracted to /kaggle/working/data/GigaVoice/GV_Train_100h\nDownloading val dataset from https://www.openslr.org/resources/118/GV_Dev_5h.tar.gz...\n100% [....................................................] 98263244 / 98263244\nExtracting val dataset...\nVal dataset extracted to /kaggle/working/data/GigaVoice/GV_Dev_5h\nDownloading test dataset from https://www.openslr.org/resources/118/GV_Eval_3h.tar.gz...\n100% [....................................................] 62063203 / 62063203\nExtracting test dataset...\nTest dataset extracted to /kaggle/working/data/GigaVoice/GV_Eval_3h\nResampling /kaggle/working/data/GigaVoice/GV_Train_100h/Audio: 100%|█| 37152/371\nResampling /kaggle/working/data/GigaVoice/GV_Dev_5h/Audio: 100%|█| 1885/1885 [00\nResampling /kaggle/working/data/GigaVoice/GV_Eval_3h/Audio: 100%|█| 1032/1032 [0\nProcessing /kaggle/working/data/GigaVoice/GV_Train_100h/text: 100%|█| 37152/3715\nCreated train_manifest.json with 37152 entries\nProcessing /kaggle/working/data/GigaVoice/GV_Dev_5h/text: 100%|█| 1885/1885 [00:\nCreated val_manifest.json with 1885 entries\nProcessing /kaggle/working/data/GigaVoice/GV_Eval_3h/text: 100%|█| 1032/1032 [00\nCreated test_manifest.json with 1032 entries\nTotal entries processed - Train: 37152, Validation: 1885, Test: 1032\n\u001b[0m", "output_type": "stream"}], "execution_count": 2}, {"cell_type": "code", "source": "##  Tokenize the language", "metadata": {"id": "GjszoJz_TzkW"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "! cp Hindi_Gram<PERSON>ani_Finetune/tokenize_language.py .\n! python tokenize_language.py", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A-TsNu7DSK_j", "outputId": "be01857c-1077-46c2-8eae-ad679fb14579", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T12:18:49.691378Z", "iopub.execute_input": "2025-06-02T12:18:49.691687Z", "iopub.status.idle": "2025-06-02T12:18:51.221296Z", "shell.execute_reply.started": "2025-06-02T12:18:49.691652Z", "shell.execute_reply": "2025-06-02T12:18:51.220592Z"}}, "outputs": [{"name": "stdout", "text": "Found 37152 texts for training\nSaved raw text corpus to tokenizer_output/document.txt\nsentencepiece_trainer.cc(78) LOG(INFO) Starts training with : \ntrainer_spec {\n  input: tokenizer_output/document.txt\n  input_format: \n  model_prefix: tokenizer_output/tokenizer\n  model_type: BPE\n  vocab_size: 1024\n  self_test_sample_size: 0\n  character_coverage: 0.9995\n  input_sentence_size: 0\n  shuffle_input_sentence: 1\n  seed_sentencepiece_size: 1000000\n  shrinking_factor: 0.75\n  max_sentence_length: 4192\n  num_threads: 16\n  num_sub_iterations: 2\n  max_sentencepiece_length: 16\n  split_by_unicode_script: 1\n  split_by_number: 1\n  split_by_whitespace: 1\n  split_digits: 0\n  pretokenization_delimiter: \n  treat_whitespace_as_suffix: 0\n  allow_whitespace_only_pieces: 0\n  required_chars: \n  byte_fallback: 0\n  vocabulary_output_piece_score: 1\n  train_extremely_large_corpus: 0\n  seed_sentencepieces_file: \n  hard_vocab_limit: 1\n  use_all_vocab: 0\n  unk_id: 0\n  bos_id: 1\n  eos_id: 2\n  pad_id: -1\n  unk_piece: <unk>\n  bos_piece: <s>\n  eos_piece: </s>\n  pad_piece: <pad>\n  unk_surface:  ⁇ \n  enable_differential_privacy: 0\n  differential_privacy_noise_level: 0\n  differential_privacy_clipping_threshold: 0\n}\nnormalizer_spec {\n  name: identity\n  add_dummy_prefix: 1\n  remove_extra_whitespaces: 0\n  escape_whitespaces: 1\n  normalization_rule_tsv: \n}\ndenormalizer_spec {}\ntrainer_interface.cc(353) LOG(INFO) SentenceIterator is not specified. Using MultiFileSentenceIterator.\ntrainer_interface.cc(185) LOG(INFO) Loading corpus: tokenizer_output/document.txt\ntrainer_interface.cc(409) LOG(INFO) Loaded all 37152 sentences\ntrainer_interface.cc(425) LOG(INFO) Adding meta_piece: <unk>\ntrainer_interface.cc(425) LOG(INFO) Adding meta_piece: <s>\ntrainer_interface.cc(425) LOG(INFO) Adding meta_piece: </s>\ntrainer_interface.cc(430) LOG(INFO) Normalizing sentences...\ntrainer_interface.cc(539) LOG(INFO) all chars count=4230049\ntrainer_interface.cc(550) LOG(INFO) Done: 99.9534% characters are covered.\ntrainer_interface.cc(560) LOG(INFO) Alphabet size=77\ntrainer_interface.cc(561) LOG(INFO) Final character coverage=0.999534\ntrainer_interface.cc(592) LOG(INFO) Done! preprocessed 37152 sentences.\ntrainer_interface.cc(598) LOG(INFO) Tokenizing input sentences with whitespace: 37152\ntrainer_interface.cc(609) LOG(INFO) Done! 31610\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=146573 min_freq=135\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=24809 size=20 all=3051 active=1809 piece=▁अ\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=13027 size=40 all=4066 active=2824 piece=िक\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=9019 size=60 all=5027 active=3785 piece=हा\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=6033 size=80 all=6257 active=5015 piece=बा\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=5273 size=100 all=7200 active=5958 piece=▁एक\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=5264 min_freq=410\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=4309 size=120 all=8262 active=2038 piece=ाय\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=3671 size=140 all=9163 active=2939 piece=▁ट\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=3312 size=160 all=9829 active=3605 piece=▁साथ\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=2792 size=180 all=10684 active=4460 piece=▁तक\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=2433 size=200 all=11328 active=5104 piece=ाना\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=2427 min_freq=366\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=2254 size=220 all=12318 active=1920 piece=ीन\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1975 size=240 all=12850 active=2452 piece=▁अन\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1804 size=260 all=13470 active=3072 piece=▁जिला\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1674 size=280 all=13947 active=3549 piece=▁क्या\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1539 size=300 all=14474 active=4076 piece=ीस\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=1538 min_freq=324\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1449 size=320 all=14857 active=1349 piece=▁बन\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1377 size=340 all=15213 active=1705 piece=ड़ा\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1316 size=360 all=15725 active=2217 piece=▁नाम\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1261 size=380 all=16303 active=2795 piece=▁रो\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1185 size=400 all=16641 active=3133 piece=▁खु\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=1178 min_freq=289\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1077 size=420 all=17048 active=1386 piece=▁चु\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=1024 size=440 all=17601 active=1939 piece=ारा\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=947 size=460 all=17979 active=2317 piece=▁खा\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=894 size=480 all=18502 active=2840 piece=▁साम\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=854 size=500 all=18754 active=3092 piece=▁विभाग\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=851 min_freq=258\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=821 size=520 all=19213 active=1457 piece=▁जाने\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=785 size=540 all=19587 active=1831 piece=▁जाती\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=742 size=560 all=20023 active=2267 piece=▁जर\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=704 size=580 all=20340 active=2584 piece=ाला\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=670 size=600 all=20772 active=3016 piece=▁पच\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=670 min_freq=225\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=647 size=620 all=21097 active=1352 piece=▁सू\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=633 size=640 all=21343 active=1598 piece=▁उन्होंने\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=613 size=660 all=21643 active=1898 piece=भाव\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=586 size=680 all=22019 active=2274 piece=ोड़\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=566 size=700 all=22219 active=2474 piece=िट\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=565 min_freq=193\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=552 size=720 all=22477 active=1313 piece=▁बैं\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=528 size=740 all=22586 active=1422 piece=ुप\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=512 size=760 all=22917 active=1753 piece=▁इसका\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=499 size=780 all=23324 active=2160 piece=▁कार्ड\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=481 size=800 all=23512 active=2348 piece=▁इला\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=480 min_freq=170\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=463 size=820 all=23921 active=1577 piece=▁हमें\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=453 size=840 all=24142 active=1798 piece=धिकारी\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=437 size=860 all=24336 active=1992 piece=ेशन\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=424 size=880 all=24583 active=2239 piece=▁देवी\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=419 size=900 all=24741 active=2397 piece=ंगल\nbpe_model_trainer.cc(159) LOG(INFO) Updating active symbols. max_freq=419 min_freq=152\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=407 size=920 all=25035 active=1514 piece=▁ज़र\nbpe_model_trainer.cc(268) LOG(INFO) Added: freq=395 size=940 all=25162 active=1641 piece=शे\ntrainer_interface.cc(687) LOG(INFO) Saving model: tokenizer_output/tokenizer.model\ntrainer_interface.cc(699) LOG(INFO) Saving vocabs: tokenizer_output/tokenizer.vocab\nTokenizer saved as tokenizer_output/tokenizer.model and tokenizer_output/tokenizer.vocab\nSaved human-readable vocabulary to tokenizer_output/vocab.txt\n\nTest encoding:\nOriginal: इस मामले में कोर्ट द्वारा निर्देश दिया गया है हाल विद्द्यालय प्रसाशन की लापरवाही ऐसी एक सौ पैसठ छात्...\nEncoded: ['▁इस', '▁माम', 'ले', '▁में', '▁को', 'र्ट', '▁द्वारा', '▁निर्', 'देश', '▁दिया', '▁गया', '▁है', '▁हाल', '▁विद्', 'द्यालय', '▁प्रसा', 'शन', '▁की', '▁ला', 'पर']...\n", "output_type": "stream"}], "execution_count": 3}, {"cell_type": "code", "source": "## Download the checkpoint", "metadata": {"id": "iou_0_JOT7DY"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "! cp Hindi_Gram<PERSON>ani_Finetune/store_parakeet.py .\n! python store_parakeet.py\n", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M79y0X9JSLCr", "outputId": "8a497d90-b3d0-4eb7-bcec-17d0282777c0", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T12:18:51.222241Z", "iopub.execute_input": "2025-06-02T12:18:51.222444Z", "iopub.status.idle": "2025-06-02T12:20:09.076415Z", "shell.execute_reply.started": "2025-06-02T12:18:51.222421Z", "shell.execute_reply": "2025-06-02T12:20:09.075191Z"}}, "outputs": [{"name": "stdout", "text": "parakeet-tdt-0.6b-v2.nemo: 100%|████████████| 2.47G/2.47G [00:22<00:00, 109MB/s]\n[NeMo I 2025-06-02 12:19:40 nemo_logging:393] Tokenizer SentencePieceTokenizer initialized with 1024 tokens\n[NeMo W 2025-06-02 12:19:41 nemo_logging:405] If you intend to do training or fine-tuning, please call the ModelPT.setup_training_data() method and provide a valid configuration file to setup the train data loader.\n    Train config : \n    use_lhotse: true\n    skip_missing_manifest_entries: true\n    input_cfg: null\n    tarred_audio_filepaths: null\n    manifest_filepath: null\n    sample_rate: 16000\n    shuffle: true\n    num_workers: 2\n    pin_memory: true\n    max_duration: 40.0\n    min_duration: 0.1\n    text_field: answer\n    batch_duration: null\n    use_bucketing: true\n    bucket_duration_bins: null\n    bucket_batch_size: null\n    num_buckets: 30\n    bucket_buffer_size: 20000\n    shuffle_buffer_size: 10000\n    \n[NeMo W 2025-06-02 12:19:41 nemo_logging:405] If you intend to do validation, please call the ModelPT.setup_validation_data() or ModelPT.setup_multiple_validation_data() method and provide a valid configuration file to setup the validation data loader(s). \n    Validation config : \n    use_lhotse: true\n    manifest_filepath: null\n    sample_rate: 16000\n    batch_size: 16\n    shuffle: false\n    max_duration: 40.0\n    min_duration: 0.1\n    num_workers: 2\n    pin_memory: true\n    text_field: answer\n    \n[NeMo I 2025-06-02 12:19:41 nemo_logging:393] PADDING: 0\n[NeMo I 2025-06-02 12:19:47 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:19:47 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:19:47 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:19:55 nemo_logging:393] Model EncDecRNNTBPEModel was successfully restored from /root/.cache/huggingface/hub/models--nvidia--parakeet-tdt-0.6b-v2/snapshots/c4b828d094af2c7238dfe03b58e0c56bc69ea57a/parakeet-tdt-0.6b-v2.nemo.\n", "output_type": "stream"}], "execution_count": 4}, {"cell_type": "code", "source": "## Prepare the finetuning configuration (Already done for you)", "metadata": {"id": "igSpAQKnUYls"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "! cp Hindi_GramVani_Finetune/finetune.py .\n! cp Hindi_GramVani_Finetune/hindi_config.yaml .\n! cat hindi_config.yaml\n", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WYjdZkiMSLFZ", "outputId": "b1aaab2c-3ca0-4895-86a2-771a059616f8", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T12:20:09.078575Z", "iopub.execute_input": "2025-06-02T12:20:09.078897Z", "iopub.status.idle": "2025-06-02T12:20:09.436139Z", "shell.execute_reply.started": "2025-06-02T12:20:09.078870Z", "shell.execute_reply": "2025-06-02T12:20:09.435161Z"}}, "outputs": [{"name": "stdout", "text": "name: \"Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi\"\n\n# use `init_from_nemo_model` or `init_from_pretrained_model` to initialize the model\n# We do not currently support `init_from_ptl_ckpt` to create a single script for all types of models.\ninit_from_nemo_model:  parakeet-tdt-0.6b-v2/parakeet-tdt-0.6b-v2.nemo  # path to nemo model\n\nmodel:\n  sample_rate: 16000\n\n  train_ds:\n    manifest_filepath: train_manifest.json\n    sample_rate: ${model.sample_rate}\n    batch_size: 4 # you may increase batch_size if your memory allows\n    shuffle: true\n    num_workers: 8\n    pin_memory: true\n    max_duration: 20\n    min_duration: 0.1\n    # tarred datasets\n    is_tarred: false\n    tarred_audio_filepaths: null\n    shuffle_n: 2048\n    # bucketing params\n    bucketing_strategy: \"fully_randomized\"\n    bucketing_batch_size: null\n\n  validation_ds:\n    manifest_filepath: val_manifest.json\n    sample_rate: ${model.sample_rate}\n    batch_size: 4\n    shuffle: false\n    use_start_end_token: false\n    num_workers: 8\n    pin_memory: true\n\n  test_ds:\n    manifest_filepath: test_manifest.json\n    sample_rate: ${model.sample_rate}\n    batch_size: 4\n    shuffle: false\n    use_start_end_token: false\n    num_workers: 8\n    pin_memory: true\n  \n  char_labels: # use for char based models\n    update_labels: false\n    labels: null # example list config: \\[' ', 'a', 'b', 'c'\\]\n\n  tokenizer: # use for spe/bpe based tokenizer models\n    update_tokenizer: true\n    dir: tokenizer_output/  # path to directory which contains either tokenizer.model (bpe) or vocab.txt (for wpe)\n    type: bpe  # Can be either bpe (SentencePiece tokenizer) or wpe (WordPiece tokenizer)\n\n  spec_augment:\n    _target_: nemo.collections.asr.modules.SpectrogramAugmentation\n    freq_masks: 2 # set to zero to disable it\n    time_masks: 10 # set to zero to disable it\n    freq_width: 27\n    time_width: 0.05\n\n  optim:\n    name: adamw\n    lr: 1e-4\n    # optimizer arguments\n    betas: [0.9, 0.98]\n    weight_decay: 1e-3\n\n    # scheduler setup\n    sched:\n      name: CosineAnnealing\n      # scheduler config override\n      warmup_steps: 5000\n      warmup_ratio: null\n      min_lr: 5e-6\n\ntrainer:\n  devices: 1 # number of GPUs, -1 would use all available GPUs\n  num_nodes: 1\n  max_epochs: 20\n  max_steps: -1 # computed at runtime if not set\n  val_check_interval: 1.0 # Set to 0.25 to check 4 times per epoch, or an int for number of iterations\n  accelerator: gpu\n  strategy:\n    _target_: lightning.pytorch.strategies.DDPStrategy\n    gradient_as_bucket_view: true\n  accumulate_grad_batches: 1\n  gradient_clip_val: 0.0\n  precision: 16 # 16, 32, or bf16\n  log_every_n_steps: 100  # Interval of logging.\n  enable_progress_bar: True\n  num_sanity_val_steps: 0 # number of steps to perform validation steps for sanity check the validation process before starting the training, setting to 0 disables it\n  check_val_every_n_epoch: 10 # number of evaluations on validation every n epochs\n  sync_batchnorm: true\n  enable_checkpointing: False  # Provided by exp_manager\n  logger: false  # Provided by exp_manager\n  benchmark: false # needs to be false for models with variable-length speech input as it slows down training\n\n\nexp_manager:\n  exp_dir: experiments/  # path to the directory where the experiment will be saved\n  name: ${name}\n  create_tensorboard_logger: true\n  create_checkpoint_callback: true\n  checkpoint_callback_params:\n    # in case of multiple validation sets, first one is used\n    monitor: \"val_wer\"\n    mode: \"min\"\n    save_top_k: 1\n    always_save_nemo: True # saves the checkpoints as nemo files along with PTL checkpoints\n  resume_if_exists: false\n  resume_ignore_no_checkpoint: false\n\n  create_wandb_logger: false\n  wandb_logger_kwargs:\n    name: null\n    project: null", "output_type": "stream"}], "execution_count": 5}, {"cell_type": "code", "source": "## We can fine now", "metadata": {"id": "LeMd6kZ6UgtE"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "! python finetune.py", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "B2lUlfCbSLH-", "outputId": "7ee94f86-64c1-4a0f-dd7e-d63f2c94a0d8", "trusted": true, "execution": {"iopub.status.busy": "2025-06-02T12:20:09.437243Z", "iopub.execute_input": "2025-06-02T12:20:09.437539Z"}}, "outputs": [{"name": "stdout", "text": "[NeMo I 2025-06-02 12:20:29 nemo_logging:393] Hydra config: name: Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi\n    init_from_nemo_model: parakeet-tdt-0.6b-v2/parakeet-tdt-0.6b-v2.nemo\n    model:\n      sample_rate: 16000\n      train_ds:\n        manifest_filepath: train_manifest.json\n        sample_rate: ${model.sample_rate}\n        batch_size: 4\n        shuffle: true\n        num_workers: 8\n        pin_memory: true\n        max_duration: 20\n        min_duration: 0.1\n        is_tarred: false\n        tarred_audio_filepaths: null\n        shuffle_n: 2048\n        bucketing_strategy: fully_randomized\n        bucketing_batch_size: null\n      validation_ds:\n        manifest_filepath: val_manifest.json\n        sample_rate: ${model.sample_rate}\n        batch_size: 4\n        shuffle: false\n        use_start_end_token: false\n        num_workers: 8\n        pin_memory: true\n      test_ds:\n        manifest_filepath: test_manifest.json\n        sample_rate: ${model.sample_rate}\n        batch_size: 4\n        shuffle: false\n        use_start_end_token: false\n        num_workers: 8\n        pin_memory: true\n      char_labels:\n        update_labels: false\n        labels: null\n      tokenizer:\n        update_tokenizer: true\n        dir: tokenizer_output/\n        type: bpe\n      spec_augment:\n        _target_: nemo.collections.asr.modules.SpectrogramAugmentation\n        freq_masks: 2\n        time_masks: 10\n        freq_width: 27\n        time_width: 0.05\n      optim:\n        name: adamw\n        lr: 0.0001\n        betas:\n        - 0.9\n        - 0.98\n        weight_decay: 0.001\n        sched:\n          name: CosineAnnealing\n          warmup_steps: 5000\n          warmup_ratio: null\n          min_lr: 5.0e-06\n    trainer:\n      devices: 1\n      num_nodes: 1\n      max_epochs: 20\n      max_steps: -1\n      val_check_interval: 1.0\n      accelerator: gpu\n      strategy:\n        _target_: lightning.pytorch.strategies.DDPStrategy\n        gradient_as_bucket_view: true\n      accumulate_grad_batches: 1\n      gradient_clip_val: 0.0\n      precision: 16\n      log_every_n_steps: 100\n      enable_progress_bar: true\n      num_sanity_val_steps: 0\n      check_val_every_n_epoch: 10\n      sync_batchnorm: true\n      enable_checkpointing: false\n      logger: false\n      benchmark: false\n    exp_manager:\n      exp_dir: experiments/\n      name: ${name}\n      create_tensorboard_logger: true\n      create_checkpoint_callback: true\n      checkpoint_callback_params:\n        monitor: val_wer\n        mode: min\n        save_top_k: 1\n        always_save_nemo: true\n      resume_if_exists: false\n      resume_ignore_no_checkpoint: false\n      create_wandb_logger: false\n      wandb_logger_kwargs:\n        name: null\n        project: null\n    \nUsing 16bit Automatic Mixed Precision (AMP)\nGPU available: True (cuda), used: True\nTPU available: False, using: 0 TPU cores\nHPU available: False, using: 0 HPUs\n`Trainer(val_check_interval=1.0)` was configured so validation will run at the end of the training epoch..\n[NeMo I 2025-06-02 12:20:29 nemo_logging:393] ExpManager schema\n[NeMo I 2025-06-02 12:20:29 nemo_logging:393] {'explicit_log_dir': None, 'exp_dir': None, 'name': None, 'version': None, 'use_datetime_version': True, 'resume_if_exists': False, 'resume_past_end': False, 'resume_ignore_no_checkpoint': False, 'resume_from_checkpoint': None, 'create_tensorboard_logger': True, 'summary_writer_kwargs': None, 'create_wandb_logger': False, 'wandb_logger_kwargs': None, 'create_mlflow_logger': False, 'mlflow_logger_kwargs': {'experiment_name': None, 'run_name': None, 'tracking_uri': None, 'tags': None, 'save_dir': './mlruns', 'prefix': '', 'artifact_location': None, 'run_id': None, 'log_model': False}, 'create_dllogger_logger': False, 'dllogger_logger_kwargs': {'verbose': False, 'stdout': False, 'json_file': './dllogger.json'}, 'create_clearml_logger': False, 'clearml_logger_kwargs': {'project': None, 'task': None, 'connect_pytorch': False, 'model_name': None, 'tags': None, 'log_model': False, 'log_cfg': False, 'log_metrics': False}, 'create_neptune_logger': False, 'neptune_logger_kwargs': None, 'create_checkpoint_callback': True, 'checkpoint_callback_params': {'filepath': None, 'dirpath': None, 'filename': None, 'monitor': 'val_loss', 'verbose': True, 'save_last': True, 'save_top_k': 3, 'save_weights_only': False, 'mode': 'min', 'auto_insert_metric_name': True, 'every_n_epochs': 1, 'every_n_train_steps': None, 'train_time_interval': None, 'prefix': None, 'postfix': '.nemo', 'save_best_model': False, 'always_save_nemo': False, 'save_nemo_on_train_end': True, 'model_parallel_size': None, 'save_on_train_epoch_end': False, 'async_save': False, 'save_last_n_optim_states': -1}, 'create_early_stopping_callback': False, 'early_stopping_callback_params': {'monitor': 'val_loss', 'mode': 'min', 'min_delta': 0.001, 'patience': 10, 'verbose': True, 'strict': True, 'check_finite': True, 'stopping_threshold': None, 'divergence_threshold': None, 'check_on_train_epoch_end': None, 'log_rank_zero_only': False}, 'create_preemption_callback': True, 'files_to_copy': None, 'log_step_timing': True, 'log_delta_step_timing': False, 'step_timing_kwargs': {'reduction': 'mean', 'sync_cuda': False, 'buffer_size': 1}, 'log_local_rank_0_only': False, 'log_global_rank_0_only': False, 'disable_validation_on_resume': True, 'ema': {'enable': False, 'decay': 0.999, 'cpu_offload': False, 'validate_original_weights': False, 'every_n_steps': 1}, 'max_time_per_run': None, 'seconds_to_sleep': 5.0, 'create_straggler_detection_callback': False, 'straggler_detection_params': {'report_time_interval': 300.0, 'calc_relative_gpu_perf': True, 'calc_individual_gpu_perf': True, 'num_gpu_perf_scores_to_log': 5, 'gpu_relative_perf_threshold': 0.7, 'gpu_individual_perf_threshold': 0.7, 'stop_if_detected': False}, 'create_fault_tolerance_callback': False, 'fault_tolerance': {'workload_check_interval': 5.0, 'initial_rank_heartbeat_timeout': 3600.0, 'rank_heartbeat_timeout': 2700.0, 'calculate_timeouts': True, 'safety_factor': 5.0, 'rank_termination_signal': <Signals.SIGKILL: 9>, 'log_level': 'INFO', 'max_rank_restarts': 0, 'max_subsequent_job_failures': 0, 'additional_ft_launcher_args': '', 'simulated_fault': None}, 'log_tflops_per_sec_per_gpu': True}\n[NeMo I 2025-06-02 12:20:29 nemo_logging:393] Experiments will be logged at experiments/Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi/2025-06-02_12-20-29\n[NeMo I 2025-06-02 12:20:29 nemo_logging:393] TensorboardLogger has been set up\n[NeMo I 2025-06-02 12:20:29 nemo_logging:393] TFLOPs per sec per GPU will be calculated, conditioned on supported models. Defaults to -1 upon failure.\n[NeMo I 2025-06-02 12:20:33 nemo_logging:393] Tokenizer SentencePieceTokenizer initialized with 1024 tokens\n[NeMo W 2025-06-02 12:20:33 nemo_logging:405] If you intend to do training or fine-tuning, please call the ModelPT.setup_training_data() method and provide a valid configuration file to setup the train data loader.\n    Train config : \n    use_lhotse: true\n    skip_missing_manifest_entries: true\n    input_cfg: null\n    tarred_audio_filepaths: null\n    manifest_filepath: null\n    sample_rate: 16000\n    shuffle: true\n    num_workers: 2\n    pin_memory: true\n    max_duration: 40.0\n    min_duration: 0.1\n    text_field: answer\n    batch_duration: null\n    use_bucketing: true\n    bucket_duration_bins: null\n    bucket_batch_size: null\n    num_buckets: 30\n    bucket_buffer_size: 20000\n    shuffle_buffer_size: 10000\n    \n[NeMo W 2025-06-02 12:20:33 nemo_logging:405] If you intend to do validation, please call the ModelPT.setup_validation_data() or ModelPT.setup_multiple_validation_data() method and provide a valid configuration file to setup the validation data loader(s). \n    Validation config : \n    use_lhotse: true\n    manifest_filepath: null\n    sample_rate: 16000\n    batch_size: 16\n    shuffle: false\n    max_duration: 40.0\n    min_duration: 0.1\n    num_workers: 2\n    pin_memory: true\n    text_field: answer\n    \n[NeMo I 2025-06-02 12:20:33 nemo_logging:393] PADDING: 0\n[NeMo I 2025-06-02 12:20:40 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:20:40 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:20:40 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:20:44 nemo_logging:393] Model EncDecRNNTBPEModel was successfully restored from /kaggle/working/parakeet-tdt-0.6b-v2/parakeet-tdt-0.6b-v2.nemo.\n[NeMo I 2025-06-02 12:20:44 nemo_logging:393] Using the tokenizer provided through config\n[NeMo W 2025-06-02 12:20:44 nemo_logging:405] You tried to register an artifact under config key=tokenizer.model_path but an artifact for it has already been registered.\n[NeMo W 2025-06-02 12:20:44 nemo_logging:405] You tried to register an artifact under config key=tokenizer.vocab_path but an artifact for it has already been registered.\n[NeMo W 2025-06-02 12:20:44 nemo_logging:405] You tried to register an artifact under config key=tokenizer.spe_tokenizer_vocab but an artifact for it has already been registered.\n[NeMo I 2025-06-02 12:20:44 nemo_logging:393] Tokenizer SentencePieceTokenizer initialized with 1024 tokens\n[NeMo I 2025-06-02 12:20:44 nemo_logging:393] Using RNNT Loss : tdt\n    Loss tdt_kwargs: {'fastemit_lambda': 0.0, 'clamp': -1.0, 'durations': [0, 1, 2, 3, 4], 'sigma': 0.02, 'omega': 0.1}\n[NeMo I 2025-06-02 12:20:44 nemo_logging:393] Changed decoder to output to ['<unk>', '<s>', '</s>', '▁क', '▁ह', '▁म', '▁स', 'ार', '▁प', '▁ब', '▁ज', '▁है', '▁के', '▁न', '्र', '▁द', 'ें', 'या', '▁र', '▁आ', '▁ल', '▁की', '▁अ', '▁व', '▁में', 'ान', '▁त', 'सी', '▁को', 'ता', '▁कर', '▁ग', '▁ऐ', 'ों', '▁ऐसी', '▁का', 'ने', '▁भ', '्य', '▁इ', 'ना', 'र्', 'िक', '▁औ', 'ही', '▁और', '▁उ', '▁च', '▁हो', '▁प्र', '▁श', 'स्', 'िल', 'िया', 'ाल', '▁ए', 'वा', '▁हैं', '▁ख', '▁य', '▁जा', 'िए', 'हा', '▁इस', 'ते', 'कार', 'ती', 'क्', 'ित', '▁दे', '▁भी', 'ाम', '्या', '▁नही', 'री', '▁सम', 'रो', 'गा', '▁स्', 'के', 'िन', '▁ने', 'बा', '▁सा', '▁लिए', 'ोग', 'िय', '▁पर', 'से', '▁वि', '▁बा', '▁से', '▁तो', 'रा', '▁हम', 'ंग', '▁नहीं', 'ारी', 'त्र', '▁ध', '▁कु', 'रोप', '▁एक', '▁आरोप', 'ला', 'िस', '▁सु', '▁मो', '▁रा', '▁ले', 'ंद', 'ैं', 'ले', '▁मु', '▁रह', '▁थ', 'नी', '▁कार', 'न्', '▁फ', '▁दो', 'ुर', 'ाय', '▁अप', 'िला', '▁आप', 'च्', 'का', 'कर', 'वार', 'भी', '▁ही', '▁जो', 'ूँ', '▁लोग', 'इल', '▁सर', '▁पा', 'ाह', 'ति', '▁मई', '▁ड', '▁ट', '▁हु', 'ाने', '▁छ', '▁रहा', 'ेश', 'बर', 'बाइल', 'ंड', '▁मोबाइल', '▁मैं', '▁वा', '्ष', 'ली', '▁सं', 'क्ष', '▁नि', '▁गया', '▁सरकार', '▁धन', '▁साथ', 'णी', '▁बि', 'ाँ', '▁रहे', '▁किया', 'गी', '▁जी', 'दा', '▁घ', '्म', 'ोज', 'ंच', '▁ला', 'की', 'ानी', '▁हूँ', '▁चाह', 'सा', '▁मिल', '▁तक', 'मार', '▁बी', 'गे', '▁रही', 'ओं', 'हाँ', 'हु', 'वाद', '▁अध', 'ियों', 'ारे', 'ेल', '▁करने', '▁नम', 'को', '▁वाणी', '▁यह', '▁या', '▁मह', 'ाना', '▁वाल', '▁आज', '्ड', 'ास', '▁उन', '▁राज', '▁धन्य', '▁धन्यवाद', 'स्त', 'हार', 'दी', 'रे', 'मा', 'त्', 'स्थ', '▁सि', 'च्च', 'गर', 'भा', 'ीन', '▁कुमार', 'टर', '▁मा', '▁श्र', '▁जिस', 'स्कार', '▁पू', '▁मे', '▁नमस्कार', 'ोल', '▁ये', '▁शिक', '▁उस', '▁कि', '▁कुछ', '▁जाए', 'ाई', '▁दिया', 'वि', '▁अन', '▁सभी', '▁दी', 'ये', '▁कम', '▁सौ', '▁बच्च', '▁अपने', 'वे', '▁भार', 'ल्', '▁अब', '▁मध', 'द्', 'रह', '▁सक', 'च्छ', '▁बार', '▁सब', '▁सी', '▁जिला', 'जार', 'टी', '▁बात', '▁कार्य', '▁रू', '▁अधिक', '▁बिहार', '▁पह', '▁वो', 'िर', 'शन', '▁उप', '▁दिन', '▁बता', '▁शिक्ष', '▁बहु', '▁किस', '▁ग्र', '▁प्रख', '▁क्या', 'चार', '▁था', 'तर', '▁बढ़', 'म्', 'ंत्र', '▁जन', '▁बहुत', '▁देख', '▁रूप', 'वारा', '▁समा', 'ँच', '▁बे', 'यी', 'ड़ी', '▁द्', '▁उन्', '▁कोई', 'ीस', '▁झ', 'ायत', '▁हुए', 'किन', 'देश', '▁तीन', '▁दु', '▁लग', 'ेंगे', 'ैसे', '▁करते', '▁लेकिन', '▁चाहिए', '्ट', 'ेर', 'कि', '▁द्वारा', '▁हमारे', '▁कारण', '▁बन', '▁बाद', '▁प्रखंड', 'ूर', '▁लेकर', '▁हजार', '▁गाँ', '▁पंच', '▁स्थ', '▁किसान', 'ोजना', 'क्षा', 'हर', 'ोता', '▁पे', '▁यहाँ', '▁लगा', '▁जान', '▁खबर', 'ूल', 'ड़ा', '▁जाता', '▁वर्', '▁होने', 'ंद्र', '▁लोगो', 'जी', '▁योजना', '▁गयी', '▁परि', 'ुल', 'लय', '▁टी', '▁देश', '▁तरह', '▁बना', '▁कह', '▁व्य', 'िश', '▁क्ष', '▁नाम', '▁ओ', '▁एव', 'जा', 'ण्ड', 'पुर', '▁हर', 'क्र', 'वन', '▁श्रोता', '▁भारत', '▁ग्राम', '▁पी', '▁निर्', '▁जब', '▁वाले', '▁पंचायत', 'गत', '▁बो', '▁कहा', '▁रो', '्त', '▁गाँव', '्यालय', '▁मधु', '▁विक', '▁करना', '▁स्व', '▁इन', '▁हुआ', '▁घर', '▁आय', '▁काम', '▁महिला', '▁अं', '▁रख', '▁मुख', '▁चौ', '▁पानी', '▁दि', '▁खु', '▁इसके', '▁उन्ह', '▁होगा', 'ेत्र', '▁किसी', '▁राज्य', '▁कई', 'धा', 'ड़े', '▁क्षेत्र', 'श्', '▁सुन', '▁अच्छ', 'वी', '▁अनु', '▁मौ', 'ौर', '▁पहले', '▁हाँ', '▁चु', '▁थी', '▁बै', 'ार्', '▁मुंग', 'स्ट', '▁अभी', 'ंत्री', '▁मुंगेर', '▁होता', 'था', 'मे', '▁सो', '▁कर्म', '▁थे', '▁समय', 'न्द', 'ष्ट', '▁मधुब', 'वर', 'ारा', '▁मधुबनी', '▁पै', '▁डी', '▁ज़', 'स्था', '▁जम', 'ंह', '▁रि', 'धान', 'ज़ार', '▁करे', '▁पि', 'र्ट', '▁बच्चों', '▁क्य', '▁वे', 'पो', '▁सिंह', '▁केंद्र', '▁खा', '▁गए', '▁प्रति', '▁जिल', '्री', '▁अगर', '▁सद', 'भाग', '्ल', 'बाद', 'मी', '▁दोस्त', 'ून', 'कारी', 'क्त', 'ंध', '▁मान', '▁साल', 'हत', '▁एस', '▁साम', '▁प्रा', '▁दस', '▁पास', 'धार', '▁मी', '▁अपना', '▁छा', '▁स्क', '▁आवा', '▁होती', 'िति', '▁ते', '▁झार', '▁फिर', '▁शिक्षा', '▁छह', '्रे', '▁बैठ', '▁झारख', '▁विभाग', '▁शुर', '▁रूपए', 'में', 'टा', '▁चल', '▁जु', '▁राम', '▁मज', '▁मन', '▁ऐसे', 'चा', '▁अपनी', '▁भा', '▁झारखण्ड', 'कों', 'टना', '▁हुई', '▁जानकारी', '▁मै', '▁जाने', '▁वर्ष', '▁जिले', 'िका', '▁बताया', '▁खेल', '▁व्यव', '▁आई', '▁स्कूल', 'सर', 'टे', '▁दोस्तों', '▁ता', '▁वही', 'द्यालय', '्यम', 'िंग', '▁हज़ार', '▁बु', '▁सम्', '▁जाती', '▁स्वा', '▁निय', '▁ठ', '▁दू', '▁तथा', 'पा', '▁अभ', '▁बोल', '▁प्रत', 'दि', 'क्रम', 'ां', '▁लिया', 'ोष', '▁रेल', 'शी', '▁लो', '▁परी', '▁क्यूँ', '▁जर', 'र्व', '▁दिल', '▁समाज', '▁पहु', '▁ऑ', 'ारह', '▁एवं', '▁रिपो', '▁खे', '▁समस्', '▁गु', 'ताल', '▁धनबाद', '▁लोगों', '▁विकास', 'जन', 'जे', '▁सरकारी', '▁मेरा', 'ाला', 'ाली', '▁पुर', '▁कार्यक्रम', '▁अव', '▁निक', 'पर', 'ोर', '्यादा', '▁परिवार', 'तन', '▁दर्', '▁जनता', '▁शुरू', 'द्ध', '▁व्यवस्था', '▁पद', 'ंबर', '▁लाख', '▁छात्र', '▁पच', '▁नंबर', '▁होगी', 'दान', 'ालय', '▁आर', '▁इससे', '▁नी', 'क्टर', 'खा', 'रीब', '▁बारे', '▁देने', '▁बद', '▁ज्यादा', '▁सह', 'ब्', '▁अग', 'धिक', '▁परीक्षा', '▁सू', '▁लाभ', '▁आपको', 'ोजन', 'योग', 'डिया', '▁शु', '▁सकते', '▁श्रोताओं', 'स्प', '▁तै', 'ोंने', '▁मंत्री', 'क्ति', '▁समस्या', 'म्म', '▁जग', '▁प्रकार', 'दन', '▁बंद', '▁उन्होंने', '▁जि', 'वाणी', 'राब', '▁शाम', '▁इसी', '▁बच', 'सार', 'िन्', '▁गरीब', '▁सकता', '▁जहाँ', 'पन', 'मान', '▁पांच', '▁वाली', '▁बिज', '▁नौ', '▁शहर', '▁जैसे', 'भाव', 'बंध', '▁उनके', 'हीं', '▁कल', '▁भर', '▁मजद', '▁बिजली', '▁परेश', 'प्त', 'ियाँ', '▁मिले', 'फी', 'ीण', 'येगा', 'इन', 'िव', '▁ना', '▁ग्रामीण', '▁बह', 'ोड़', '▁कभी', '▁जगह', '▁पढ़', 'ड़क', '▁उठ', '▁जल', '▁माध', '▁सुर', '▁भारती', '▁मीडिया', '▁उसके', '▁सदर', '▁डॉ', '▁प्रधान', 'धी', '▁आम', '▁मत', '▁टीम', '▁एवम', 'िट', '▁मांग', 'माण', '▁अच्छा', '▁पड़', 'ुई', '▁बच्चे', '▁उत्', '▁पाँच', 'विधा', '▁अधिकारी', 'ियो', '▁इत', '▁जायेगा', '▁इं', '▁लि', '▁माध्यम', '▁यु', '▁सफ', '▁हि', '▁बैं', '▁कहीं', '▁तैय', '▁तु', '▁चला', 'न्द्र', '▁सके', '▁वहाँ', '▁उन्हें', 'झे', '▁सात', '▁किए', '▁आपका', '▁सबसे', '▁चार', '▁जल्', '▁चाहता', '▁बीच', '▁डॉक्टर', '▁समाचार', 'ुप', 'मारी', 'र्ण', '▁पिछ', 'वल', '▁सुविधा', '▁वह', 'नों', 'रिया', '▁करा', 'क्ट', '▁आधार', '▁नई', 'जू', '्यक्ष', '▁जिससे', '▁पहुँच', 'म्बर', '▁रिपोर्ट', '▁प्रदेश', '▁इसका', 'प्', 'सल', '▁कहना', '▁ऐसा', '▁तहत', 'डी', '▁बारिश', '▁काफी', '▁बैठक', '▁पुल', '▁जाते', 'र्म', 'रण', 'ेट', 'लिए', 'तु', '▁आने', '▁तब', '▁पु', '▁कार्ड', '▁अठ', '▁अस्प', '▁माँ', '▁अस्पताल', 'वाई', '▁आठ', '▁महिलाओं', 'ूम', '▁जमुई', '▁बड़ी', '▁सुब', '▁गा', '▁शौ', '▁दौर', '▁जीवन', '▁सही', '▁मध्य', '▁कृ', 'िये', '▁इला', 'धि', 'तीस', 'चित', '▁देना', '▁प्रश', '▁जारी', 'र्ड', '▁सुबह', 'गार', '▁तर', '▁सेवा', 'पी', 'ंट', '▁स्ट', '▁हा', 'ोट', '्ञ', '▁कॉ', '▁शौच', '▁हमें', 'दार', 'त्म', '▁गई', '▁सड़क', \"▁'\", 'कारो', '▁पता', '▁निर्माण', '▁अठारह', '▁कै', '▁आगे', '▁मजदूर', '▁बोकारो', 'ंप', '▁नए', 'ंगा', '▁आपके', '▁ली', '▁बजे', 'धिकारी', '▁अधिकार', 'वान', 'ख्या', '▁जाएगी', '▁बनाने', '▁रे', '▁अन्य', 'ंदी', '▁क्यूँकी', '▁दीदी', '▁फ़', 'दाब', '▁आदाब', '▁रेलवे', '▁उसे', '▁अध्यक्ष', 'र्थ', '▁इसमें', '▁M', 'ेशन', '▁जिन', '▁पदा', '▁देखा', '▁फै', '▁ताकि', '▁भारतीय', 'वेदन', 'मल', '▁गर्', '▁पिछले', 'तो', 'बीस', '▁दिव', '▁दिनों', '▁खास', '▁होते', 'वाल', '▁टे', '▁हाल', '▁देवी', '▁स्वास्थ', 'ुष', '▁गो', '▁विश्', '▁पुलिस', '▁प्रसा', '▁ट्रे', '▁स्थिति', 'co', 'et', 'in', 'mp', '▁#', 'let', '▁आस', 'comp', 'ियान', 'incomp', 'सम', 'ंगल', 'incomplet', 'ट्र', 'incomplete', '▁विद्', '▁वेतन', 'जल', 'नि', 'नो', '▁आये', '▁करके', '▁खिला', '▁बर', '▁दबा', '▁बैंक', '▁सकती', '▁मंड', 'कान', '▁मिला', '▁ख़', '▁ज़र', '्यान', '▁आदि', '▁करोड़', '▁तू', '▁पत्र', '▁सुर्', '▁नगर', '▁माम', '▁रोक', '▁करती', 'चना', '▁करता', '▁निकाल', '▁अंत', '▁चाल', '▁महा', '▁देते', '▁होना', '▁इसलिए', 'शे', 'धर', '▁दूस', '▁दौरान', 'बी', '▁', 'ा', 'र', 'क', 'े', 'ी', 'स', 'ह', 'न', '्', 'म', 'ि', 'ो', 'ं', 'त', 'ल', 'प', 'य', 'ब', 'द', 'ज', 'व', 'ग', 'ै', 'ु', 'च', 'आ', 'ए', 'श', 'भ', 'अ', 'ट', 'ू', 'ख', 'ध', 'इ', 'ऐ', 'थ', 'ई', 'ड', 'ँ', 'औ', 'ष', 'उ', 'ण', 'फ', 'छ', 'ड़', 'ौ', 'ओ', 'ठ', 'घ', 'ज़', 'झ', 'ढ़', 'ॉ', 'ृ', \"'\", 'फ़', 'e', 'ढ', 'ऑ', 'A', 'ख़', 'ऊ', 'ञ', 'M', 'i', 't', 'p', 'l', 'm', 'n', '#', 'c', 'o', 'ः'] vocabulary.\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] Dataset loaded with 36797 files totalling 97.91 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] 355 files were filtered totalling 2.98 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] Dataset loaded with 1885 files totalling 5.02 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] 0 files were filtered totalling 0.00 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] Dataset loaded with 1032 files totalling 2.80 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] 0 files were filtered totalling 0.00 hours\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] Optimizer config = AdamW (\n    Parameter Group 0\n        amsgrad: False\n        betas: [0.9, 0.98]\n        capturable: False\n        differentiable: False\n        eps: 1e-08\n        foreach: None\n        fused: None\n        lr: 0.0001\n        maximize: False\n        weight_decay: 0.001\n    )\n[NeMo I 2025-06-02 12:20:47 nemo_logging:393] Scheduler \"<nemo.core.optim.lr_scheduler.CosineAnnealing object at 0x7a17aba44950>\" \n    will be used during training (effective maximum steps = 184000) - \n    Parameters : \n    (warmup_steps: 5000\n    warmup_ratio: null\n    min_lr: 5.0e-06\n    max_steps: 184000\n    )\nINFO: Initializing distributed: GLOBAL_RANK: 0, MEMBER: 1/1\nInitializing distributed: GLOBAL_RANK: 0, MEMBER: 1/1\n----------------------------------------------------------------------------------------------------\ndistributed_backend=nccl\nAll distributed processes registered. Starting with 1 processes\n----------------------------------------------------------------------------------------------------\n\n2025-06-02 12:20:50.587414: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\nWARNING: All log messages before absl::InitializeLog() is called are written to STDERR\nE0000 00:00:1748866850.778886     581 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\nE0000 00:00:1748866850.836686     581 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\nINFO: LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\nLOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]\n[NeMo I 2025-06-02 12:21:01 nemo_logging:393] Optimizer config = AdamW (\n    Parameter Group 0\n        amsgrad: False\n        betas: [0.9, 0.98]\n        capturable: False\n        differentiable: False\n        eps: 1e-08\n        foreach: None\n        fused: None\n        lr: 0.0001\n        maximize: False\n        weight_decay: 0.001\n    )\n[NeMo I 2025-06-02 12:21:01 nemo_logging:393] Scheduler \"<nemo.core.optim.lr_scheduler.CosineAnnealing object at 0x7a171098fd90>\" \n    will be used during training (effective maximum steps = 184000) - \n    Parameters : \n    (warmup_steps: 5000\n    warmup_ratio: null\n    min_lr: 5.0e-06\n    max_steps: 184000\n    )\nINFO: \n  | Name              | Type                              | Params | Mode \n--------------------------------------------------------------------------------\n0 | preprocessor      | AudioToMelSpectrogramPreprocessor | 0      | train\n1 | encoder           | ConformerEncoder                  | 608 M  | train\n2 | spec_augmentation | SpectrogramAugmentation           | 0      | train\n3 | wer               | WER                               | 0      | train\n4 | joint             | RNNTJoint                         | 1.7 M  | train\n5 | decoder           | RNNTDecoder                       | 7.2 M  | train\n6 | loss              | RNNTLoss                          | 0      | train\n7 | spec_augment      | SpectrogramAugmentation           | 0      | train\n--------------------------------------------------------------------------------\n617 <USER>     <GROUP> params\n0         Non-trainable params\n617 M     Total params\n2,471.304 Total estimated model params size (MB)\n708       Modules in train mode\n0         Modules in eval mode\n\n  | Name              | Type                              | Params | Mode \n--------------------------------------------------------------------------------\n0 | preprocessor      | AudioToMelSpectrogramPreprocessor | 0      | train\n1 | encoder           | ConformerEncoder                  | 608 M  | train\n2 | spec_augmentation | SpectrogramAugmentation           | 0      | train\n3 | wer               | WER                               | 0      | train\n4 | joint             | RNNTJoint                         | 1.7 M  | train\n5 | decoder           | RNNTDecoder                       | 7.2 M  | train\n6 | loss              | RNNTLoss                          | 0      | train\n7 | spec_augment      | SpectrogramAugmentation           | 0      | train\n--------------------------------------------------------------------------------\n617 <USER>     <GROUP> params\n0         Non-trainable params\n617 M     Total params\n2,471.304 Total estimated model params size (MB)\n708       Modules in train mode\n0         Modules in eval mode\nEpoch 0:   0%|                                         | 0/9200 [00:00<?, ?it/s][NeMo I 2025-06-02 12:21:02 nemo_logging:393] Disabled CUDA graphs for module <class 'nemo.collections.asr.models.rnnt_bpe_models.EncDecRNNTBPEModel'>.decoding.decoding\n[NeMo I 2025-06-02 12:21:02 nemo_logging:393] Disabled CUDA graphs for module <class 'nemo.collections.asr.metrics.wer.WER'>wer.decoding.decoding\n[NeMo W 2025-06-02 12:21:04 nemo_logging:405] Provided RNNT Joint tensor is of dtype torch.float16, but RNNT loss could not be calculated in fp16 due to following reason stated below. Loss will be calculated in fp32. \n    \n    Env variable `NUMBA_CUDA_USE_NVIDIA_BINDING` is not available or has not set to `1`.Numba CUDA FP16 is supported in installed numba version.\n[NeMo W 2025-06-02 12:21:07 nemo_logging:405] /usr/local/lib/python3.11/dist-packages/numba_cuda/numba/cuda/dispatcher.py:579: NumbaPerformanceWarning: \u001b[1mGrid size 4 will likely result in GPU under-utilization due to low occupancy.\u001b[0m\n      warn(NumbaPerformanceWarning(msg))\n    \n[NeMo W 2025-06-02 12:21:07 nemo_logging:405] /usr/local/lib/python3.11/dist-packages/numba_cuda/numba/cuda/dispatcher.py:579: NumbaPerformanceWarning: \u001b[1mGrid size 4 will likely result in GPU under-utilization due to low occupancy.\u001b[0m\n      warn(NumbaPerformanceWarning(msg))\n    \n[NeMo W 2025-06-02 12:21:08 nemo_logging:405] /usr/local/lib/python3.11/dist-packages/numba_cuda/numba/cuda/dispatcher.py:579: NumbaPerformanceWarning: \u001b[1mGrid size 1 will likely result in GPU under-utilization due to low occupancy.\u001b[0m\n      warn(NumbaPerformanceWarning(msg))\n    \nEpoch 0:   0%| | 1/9200 [00:06<17:00:24,  0.15it/s, v_num=0-29, train_step_timin[NeMo W 2025-06-02 12:21:09 nemo_logging:405] /usr/local/lib/python3.11/dist-packages/numba_cuda/numba/cuda/dispatcher.py:579: NumbaPerformanceWarning: \u001b[1mGrid size 4 will likely result in GPU under-utilization due to low occupancy.\u001b[0m\n      warn(NumbaPerformanceWarning(msg))\n    \n[NeMo W 2025-06-02 12:21:09 nemo_logging:405] /usr/local/lib/python3.11/dist-packages/numba_cuda/numba/cuda/dispatcher.py:579: NumbaPerformanceWarning: \u001b[1mGrid size 1 will likely result in GPU under-utilization due to low occupancy.\u001b[0m\n      warn(NumbaPerformanceWarning(msg))\n    \nEpoch 0:   1%| | 99/9200 [01:09<1:46:58,  1.42it/s, v_num=0-29, train_step_timin[NeMo I 2025-06-02 12:22:12 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:22:12 nemo_logging:393] reference:इ तो बहुत बड़ा फायदा भी है मगर अभी सरकार की नीतियों की वजह बहुत से लोगों को इससे आधार के होने की वजह से काफ़ी क्षति हुई है\n[NeMo I 2025-06-02 12:22:12 nemo_logging:393] predicted:स्क\nEpoch 0:   2%| | 199/9200 [02:15<1:42:20,  1.47it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:23:18 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:23:18 nemo_logging:393] reference:कर सकती हैं क्यूँकी माँ पिता का देखभाल का ज़िम्मा उसका हैं कठिन डगर पर आगे बढ़ेगी पिता की अस्थिया का विसर्जन करेगी किरण भरी आँखों से किरण\n[NeMo I 2025-06-02 12:23:18 nemo_logging:393] predicted:\nEpoch 0:   3%| | 299/9200 [03:21<1:39:47,  1.49it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:24:23 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:24:23 nemo_logging:393] reference:पिछले सौ पिछले पंचायत वाली तहसील\n[NeMo I 2025-06-02 12:24:23 nemo_logging:393] predicted:\nEpoch 0:   4%| | 399/9200 [04:25<1:37:36,  1.50it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:25:28 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:25:28 nemo_logging:393] reference:नमस्कार जोहार श्रोताओ मोबाइल वाणी पर आप सुन रहे सुबह की सुर्खिया और मैं हूँ आपका अपना दोस्त  ⁇ षि\n[NeMo I 2025-06-02 12:25:28 nemo_logging:393] predicted:\nEpoch 0:   5%| | 499/9200 [05:30<1:35:54,  1.51it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:26:32 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:26:32 nemo_logging:393] reference:दिखाया गया है\n[NeMo I 2025-06-02 12:26:32 nemo_logging:393] predicted:स्क बजे\nEpoch 0:   7%| | 599/9200 [06:33<1:34:11,  1.52it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:27:36 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:27:36 nemo_logging:393] reference:मई पकोड़ा पंचायत का मुखिया राम प्रवेश पासवान बोल आर एच A हूँ\n[NeMo I 2025-06-02 12:27:36 nemo_logging:393] predicted:\nEpoch 0:   8%| | 699/9200 [07:39<1:33:03,  1.52it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:28:41 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:28:41 nemo_logging:393] reference:एसल के जाने के बाद बिजली कनेक्शन में हो रही परेशानी जल्द ही दूर हो जाएगी एन बी पी डी सी एल इक्कीस अगस्त से शहर\n[NeMo I 2025-06-02 12:28:41 nemo_logging:393] predicted:\nEpoch 0:   9%| | 799/9200 [08:42<1:31:33,  1.53it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:29:45 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:29:45 nemo_logging:393] reference:खेल मंत्रालय ने गुरुवार को ये घोषणा खेल मंत्रालय ने पुरस्कार विजेता की सूची अधिकारिक तौर पर जारी की रामनाथ कोविंद राष्ट्रपति भवन मैं इन खिलाड़ी ओ को सम्मानित करेगी\n[NeMo I 2025-06-02 12:29:45 nemo_logging:393] predicted:\nEpoch 0:  10%| | 899/9200 [09:46<1:30:12,  1.53it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:30:48 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:30:48 nemo_logging:393] reference:मुझे घूमने का बहुत शौक हैं और इससे बात पे तुमने मुझे एक जोक सूना और वो भी अच्छा वाला हाँ क्यूँ नहीं तेरे लिए तो कुछ भी यार दीदी सूना रही हैं मैं सुनिधि रांची ऐसी\n[NeMo I 2025-06-02 12:30:48 nemo_logging:393] predicted:\nEpoch 0:  11%| | 999/9200 [10:50<1:28:58,  1.54it/s, v_num=0-29, train_step_timi[NeMo I 2025-06-02 12:31:53 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:31:53 nemo_logging:393] reference:आर आर क्लब गोतवना ऐसी मई संतोष कुमार बिहार मोबाइलवाणी के श्रोता आज की शीर्षक शौक यही अरमान यही कुछ कर दिखलायेंगे सामाजिक  ⁇ 'मुद्दों' ⁇  'को' ⁇  सरकारों टोक पहुचायेंगे\n[NeMo I 2025-06-02 12:31:53 nemo_logging:393] predicted:\nEpoch 0:  12%| | 1099/9200 [11:54<1:27:47,  1.54it/s, v_num=0-29, train_step_tim[NeMo I 2025-06-02 12:32:57 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:32:57 nemo_logging:393] reference:ध्यानपूर्वक पढ़ ले गारी गारी आर डी गारी A द्वारा जनता को यह सूचित किया\n[NeMo I 2025-06-02 12:32:57 nemo_logging:393] predicted:\nEpoch 0:  13%|▏| 1199/9200 [13:00<1:26:46,  1.54it/s, v_num=0-29, train_step_tim[NeMo I 2025-06-02 12:34:02 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:34:02 nemo_logging:393] reference:जिस के कारण यहाँ के लोगो को पानी उत्पन हो गयी हैं तालाब सुख गए हैं यहाँ के कारण खदान मैं हैं और जो\n[NeMo I 2025-06-02 12:34:02 nemo_logging:393] predicted:\nEpoch 0:  14%|▏| 1299/9200 [14:04<1:25:36,  1.54it/s, v_num=0-29, train_step_tim[NeMo I 2025-06-02 12:35:07 nemo_logging:393] \n    \n[NeMo I 2025-06-02 12:35:07 nemo_logging:393] reference:अपनों ने ही हमें बना रहे गुलाम सत असत्य के बीच लड़ाई में लड़ कर हासिल कर\n[NeMo I 2025-06-02 12:35:07 nemo_logging:393] predicted:\nEpoch 0:  14%|▏| 1303/9200 [14:07<1:25:33,  1.54it/s, v_num=0-29, train_step_tim", "output_type": "stream"}], "execution_count": null}, {"cell_type": "code", "source": "", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A5_FwNcQSLK8", "outputId": "c3a0965d-f3a1-4d3e-a072-a3ee40977fb6", "trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ODPQyKcqad7l", "outputId": "484a404d-b4f9-4b50-b2ca-8cba3e7b4871", "trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "", "metadata": {"id": "czvBo5nQdlVP"}, "outputs": [], "execution_count": null}]}