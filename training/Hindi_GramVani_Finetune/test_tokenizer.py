#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sentencepiece as spm
import json

# Load the trained tokenizer
sp = spm.SentencePieceProcessor()
sp.load('tokenizer_output/tokenizer.model')

# Test Vietnamese sentences
test_sentences = [
    "<PERSON><PERSON> chào, tôi là người Việt Nam.",
    "Hôm nay trời đẹp quá, chúng ta đi chơi nhé!",
    "Tôi đang học tiếng Việt và rất thích ngôn ngữ này.",
    "<PERSON><PERSON><PERSON> thứ năm bố đi chợ, bố hay lời mua đồ của cô.",
    "28 tết á, bà già đang nấu cơm, đang rửa ông bà."
]

print("=== TESTING VIETNAMESE TOKENIZER ===")
print(f"Vocabulary size: {sp.get_piece_size()}")
print()

for i, sentence in enumerate(test_sentences, 1):
    print(f"Test {i}:")
    print(f"Original: {sentence}")
    
    # Encode to pieces
    pieces = sp.encode_as_pieces(sentence)
    print(f"Pieces: {pieces}")
    
    # Encode to IDs
    ids = sp.encode_as_ids(sentence)
    print(f"IDs: {ids}")
    
    # Decode back
    decoded = sp.decode_pieces(pieces)
    print(f"Decoded: {decoded}")
    
    # Check if encoding/decoding is lossless
    is_lossless = sentence == decoded
    print(f"Lossless: {is_lossless}")
    
    if not is_lossless:
        print(f"WARNING: Encoding/decoding is not lossless!")
        print(f"Original: '{sentence}'")
        print(f"Decoded:  '{decoded}'")
    
    print("-" * 50)

# Test with manifest data
print("\n=== TESTING WITH MANIFEST DATA ===")
manifest_path = '/home/<USER>/nemo_asr/training/datasets/Vietnamese_ASR_TestingData/train_manifest.json'

with open(manifest_path, 'r', encoding='utf-8') as f:
    for i, line in enumerate(f):
        if i >= 3:  # Test only first 3 entries
            break
        
        data = json.loads(line.strip())
        text = data['text']
        
        print(f"Manifest entry {i+1}:")
        print(f"Text: {text}")
        
        pieces = sp.encode_as_pieces(text)
        decoded = sp.decode_pieces(pieces)
        
        print(f"Pieces count: {len(pieces)}")
        print(f"First 10 pieces: {pieces[:10]}")
        print(f"Lossless: {text == decoded}")
        
        if text != decoded:
            print(f"WARNING: Lossless encoding failed!")
            print(f"Original: '{text}'")
            print(f"Decoded:  '{decoded}'")
        
        print("-" * 50)

print("\nTokenizer test completed!")
