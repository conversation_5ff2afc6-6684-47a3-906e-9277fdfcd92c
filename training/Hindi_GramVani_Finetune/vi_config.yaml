name: "Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi"

# use `init_from_nemo_model` or `init_from_pretrained_model` to initialize the model
# We do not currently support `init_from_ptl_ckpt` to create a single script for all types of models.
# init_from_nemo_model:  parakeet-tdt-0.6b-v2/parakeet-tdt-0.6b-v2.nemo  # path to nemo model
init_from_nemo_model: /home/<USER>/nemo_asr/training/Hindi_GramVani_Finetune/experiments/Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi/2025-06-13_12-26-53/checkpoints/Speech_To_Text_Finetuning_parakeet_v2_tdt_hindi.nemo
model:
  sample_rate: 16000
  datasets_dir: /home/<USER>/nemo_asr/training/datasets

  train_ds:
    manifest_filepath: ${model.datasets_dir}/leduckhai/VietMed-NER/train_manifest.json
    sample_rate: ${model.sample_rate}
    batch_size: 32 # Increased to use more VRAM (you have ~15GB free)
    bucketing_batch_size: 32 # Match with batch_size
    shuffle: true
    num_workers: 8
    pin_memory: true
    max_duration: 20
    # min_duration: 0.1
    # tarred datasets
    is_tarred: false
    tarred_audio_filepaths: null
    shuffle_n: 2048
    # bucketing params
    bucketing_strategy: "fully_randomized"
    min_duration: 0.5  # Tăng min duration
    normalize_transcripts: true  # Quan trọng cho tiếng Việt
    trim_silence: true
    use_start_end_token: true
    channel_selector: null

  validation_ds:
    manifest_filepath: ${model.datasets_dir}/leduckhai/VietMed-NER-val/validation_manifest.json
    sample_rate: ${model.sample_rate}
    batch_size: 32 # Increased validation batch size
    shuffle: false
    use_start_end_token: false
    num_workers: 8
    pin_memory: true
    normalize_transcripts: true
    trim_silence: true

  test_ds:
    manifest_filepath: ${model.datasets_dir}/leduckhai/VietMed-NER-test/test_manifest.json # Using val as test
    sample_rate: ${model.sample_rate}
    batch_size: 32 # Increased test batch size
    shuffle: false
    use_start_end_token: false
    num_workers: 8
    pin_memory: true
  
  char_labels: # use for char based models
    update_labels: false
    labels: null # example list config: \[' ', 'a', 'b', 'c'\]

  tokenizer: # use for spe/bpe based tokenizer models
    update_tokenizer: true
    dir: tokenizer_output/  # path to directory which contains either tokenizer.model (bpe) or vocab.txt (for wpe)
    type: bpe  # Can be either bpe (SentencePiece tokenizer) or wpe (WordPiece tokenizer)
    vocab_size: 500

  # tokenizer:
  #   _target_: nemo.collections.common.tokenizers.vietnamese_tokenizer.VietnameseCharsTokenizer
  #   punct: true
  #   apostrophe: true

  encoder_config:
    freeze: false  # Whether to freeze the weight of encoder 
  spec_augment:
    _target_: nemo.collections.asr.modules.SpectrogramAugmentation
    # freq_masks: 2 # set to zero to disable it
    # time_masks: 10 # set to zero to disable it
    # freq_width: 27
    # time_width: 0.05
    _target_: nemo.collections.asr.modules.SpectrogramAugmentation
    freq_masks: 2
    time_masks: 6  # Giảm từ 10 xuống 5
    freq_width: 25  # Giảm từ 27 xuống 20
    time_width: 0.04  # Giảm từ 0.05 xuống 0.03
    mask_value: 0.0


  optim:
    name: adamw
    lr: 1e-4  #1e-4 5e-5 
    # optimizer arguments
    betas: [0.9, 0.98]
    weight_decay: 1e-3

    # scheduler setup
    sched:
      name: CosineAnnealing
      # scheduler config override
      warmup_steps: 5000
      warmup_ratio: null
      min_lr: 5e-6

trainer:
  devices: -1 # number of GPUs, -1 would use all available GPUs
  num_nodes: 1
  max_epochs: 50
  max_steps: -1 # computed at runtime if not set
  # val_check_interval: 1.0 # Set to 0.25 to check 4 times per epoch, or an int for number of iterations
  accelerator: gpu
  strategy:
    _target_: lightning.pytorch.strategies.DDPStrategy
    gradient_as_bucket_view: true
  accumulate_grad_batches: 4 # Increase to simulate larger batch size
  # gradient_clip_val: 0.0
  precision: 16 # 16, 32, or bf16
  log_every_n_steps: 2000  # Interval of logging.
  enable_progress_bar: True
  num_sanity_val_steps: 0 # number of steps to perform validation steps for sanity check the validation process before starting the training, setting to 0 disables it
  # check_val_every_n_epoch: 10 # number of evaluations on validation every n epochs
  sync_batchnorm: true
  enable_checkpointing: False  # Provided by exp_manager
  logger: false  # Provided by exp_manager
  benchmark: false # needs to be false for models with variable-length speech input as it slows down training
  gradient_clip_val: 1.0  # Bật gradient clipping
  val_check_interval: 0.5  # Kiểm tra validation thường xuyên hơn
  check_val_every_n_epoch: 5  # Giảm frequency


exp_manager:
  exp_dir: experiments/  # path to the directory where the experiment will be saved
  name: ${name}
  create_tensorboard_logger: true
  create_checkpoint_callback: true
  checkpoint_callback_params:
    # in case of multiple validation sets, first one is used
    monitor: "val_wer"
    mode: "min"
    save_top_k: 1
    always_save_nemo: True # saves the checkpoints as nemo files along with PTL checkpoints
  resume_if_exists: false
  resume_ignore_no_checkpoint: false
  monitor: "val_wer"


  create_wandb_logger: false
  wandb_logger_kwargs:
  project: "vietnamese-asr-parakeet"
  name: "parakeet-tdt-vi-finetuning"