[NeMo W 2025-06-13 12:26:59 nemo_logging:405] If you intend to do training or fine-tuning, please call the ModelPT.setup_training_data() method and provide a valid configuration file to setup the train data loader.
    Train config : 
    use_lhotse: true
    skip_missing_manifest_entries: true
    input_cfg: null
    tarred_audio_filepaths: null
    manifest_filepath: null
    sample_rate: 16000
    shuffle: true
    num_workers: 2
    pin_memory: true
    max_duration: 40.0
    min_duration: 0.1
    text_field: answer
    batch_duration: null
    use_bucketing: true
    bucket_duration_bins: null
    bucket_batch_size: null
    num_buckets: 30
    bucket_buffer_size: 20000
    shuffle_buffer_size: 10000
    
[NeMo W 2025-06-13 12:26:59 nemo_logging:405] If you intend to do validation, please call the ModelPT.setup_validation_data() or ModelPT.setup_multiple_validation_data() method and provide a valid configuration file to setup the validation data loader(s). 
    Validation config : 
    use_lhotse: true
    manifest_filepath: null
    sample_rate: 16000
    batch_size: 16
    shuffle: false
    max_duration: 40.0
    min_duration: 0.1
    num_workers: 2
    pin_memory: true
    text_field: answer
    
[NeMo W 2025-06-13 12:27:08 nemo_logging:405] You tried to register an artifact under config key=tokenizer.model_path but an artifact for it has already been registered.
[NeMo W 2025-06-13 12:27:08 nemo_logging:405] You tried to register an artifact under config key=tokenizer.vocab_path but an artifact for it has already been registered.
[NeMo W 2025-06-13 12:27:08 nemo_logging:405] You tried to register an artifact under config key=tokenizer.spe_tokenizer_vocab but an artifact for it has already been registered.
[NeMo W 2025-06-13 12:27:09 nemo_logging:405] The vocabulary size of the new tokenizer differs from that of the loaded model. As a result, finetuning will proceed with the new vocabulary, and the decoder will be reinitialized.
[NeMo W 2025-06-13 12:27:12 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/lightning/pytorch/loops/fit_loop.py:298: The number of training batches (142) is smaller than the logging interval Trainer(log_every_n_steps=2000). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.
    
[NeMo W 2025-06-13 12:27:15 nemo_logging:405] Provided RNNT Joint tensor is of dtype torch.float16, but RNNT loss could not be calculated in fp16 due to following reason stated below. Loss will be calculated in fp32. 
    
    Env variable `NUMBA_CUDA_USE_NVIDIA_BINDING` is not available or has not set to `1`.Numba CUDA FP16 is supported in installed numba version.
[NeMo W 2025-06-13 12:27:16 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 12:27:16 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 12:27:17 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 1 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 12:27:17 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 12:27:17 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 1 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 12:30:32 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 3 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-13 13:00:33 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:431: It is recommended to use `self.log('global_step', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
    
