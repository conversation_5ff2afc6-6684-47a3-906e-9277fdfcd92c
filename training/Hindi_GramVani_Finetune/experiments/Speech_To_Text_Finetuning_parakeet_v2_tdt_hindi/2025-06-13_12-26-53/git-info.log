commit hash: 0bd6d108338fe4eaa9027bc81bc2603098ed7960
diff --git a/hindi_config.yaml b/hindi_config.yaml
index 1e3a4ab..9193a60 100644
--- a/hindi_config.yaml
+++ b/hindi_config.yaml
@@ -76,7 +76,7 @@ model:
       min_lr: 5e-6
 
 trainer:
-  devices: 1 # number of GPUs, -1 would use all available GPUs
+  devices: -1 # number of GPUs, -1 would use all available GPUs
   num_nodes: 1
   max_epochs: 50
   max_steps: -1 # computed at runtime if not set
diff --git a/tokenize_language.py b/tokenize_language.py
index b7e7b99..bdcfd81 100644
--- a/tokenize_language.py
+++ b/tokenize_language.py
@@ -8,7 +8,9 @@ os.makedirs('tokenizer_output', exist_ok=True)
 
 # Extract texts from manifest
 texts = []
-with open('train_manifest.json', 'r', encoding='utf-8') as f:
+# manifest_path = '/home/<USER>/nemo_asr/training/datasets/Vietnamese_ASR_TestingData/train_manifest.json'
+manifest_path = '/home/<USER>/nemo_asr/training/datasets/leduckhai/VietMed-NER/train_manifest.json'
+with open(manifest_path, 'r', encoding='utf-8') as f:
     for line in f:
         data = json.loads(line.strip())
         if 'text' in data and data['text'].strip():
@@ -28,13 +30,17 @@ model_prefix = 'tokenizer_output/tokenizer'
 spm.SentencePieceTrainer.train(
     input=document_file,  # Now using document.txt directly
     model_prefix=model_prefix,
-    vocab_size=1024,
+    vocab_size=2048,  # Increased vocab size for Vietnamese
     model_type='bpe',
-    character_coverage=0.9995,
-    normalization_rule_name='identity',
+    character_coverage=0.9999,  # Higher coverage for Vietnamese characters
+    normalization_rule_name='identity',  # Keep original characters
     remove_extra_whitespaces=False,
     max_sentence_length=4192,
-    shuffle_input_sentence=True
+    shuffle_input_sentence=True,
+    pad_id=0,
+    unk_id=1,
+    bos_id=2,
+    eos_id=3
 )
 
 print(f"Tokenizer saved as {model_prefix}.model and {model_prefix}.vocab")
