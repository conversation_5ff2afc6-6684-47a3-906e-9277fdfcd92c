[NeMo W 2025-06-14 03:35:07 nemo_logging:405] If you intend to do training or fine-tuning, please call the ModelPT.setup_training_data() method and provide a valid configuration file to setup the train data loader.
    Train config : 
    manifest_filepath: /home/<USER>/nemo_asr/training/datasets/leduckhai/VietMed-NER/train_manifest.json
    sample_rate: 16000
    batch_size: 32
    shuffle: true
    num_workers: 8
    pin_memory: true
    max_duration: 20
    min_duration: 0.1
    is_tarred: false
    tarred_audio_filepaths: null
    shuffle_n: 2048
    bucketing_strategy: fully_randomized
    bucketing_batch_size: 32
    
[NeMo W 2025-06-14 03:35:07 nemo_logging:405] If you intend to do validation, please call the ModelPT.setup_validation_data() or ModelPT.setup_multiple_validation_data() method and provide a valid configuration file to setup the validation data loader(s). 
    Validation config : 
    manifest_filepath: /home/<USER>/nemo_asr/training/datasets/leduckhai/VietMed-NER-val/validation_manifest.json
    sample_rate: 16000
    batch_size: 32
    shuffle: false
    use_start_end_token: false
    num_workers: 8
    pin_memory: true
    
[NeMo W 2025-06-14 03:35:07 nemo_logging:405] Please call the ModelPT.setup_test_data() or ModelPT.setup_multiple_test_data() method and provide a valid configuration file to setup the test data loader(s).
    Test config : 
    manifest_filepath: /home/<USER>/nemo_asr/training/datasets/leduckhai/VietMed-NER-test/test_manifest.json
    sample_rate: 16000
    batch_size: 32
    shuffle: false
    use_start_end_token: false
    num_workers: 8
    pin_memory: true
    
[NeMo W 2025-06-14 03:35:17 nemo_logging:405] You tried to register an artifact under config key=tokenizer.model_path but an artifact for it has already been registered.
[NeMo W 2025-06-14 03:35:17 nemo_logging:405] You tried to register an artifact under config key=tokenizer.vocab_path but an artifact for it has already been registered.
[NeMo W 2025-06-14 03:35:17 nemo_logging:405] You tried to register an artifact under config key=tokenizer.spe_tokenizer_vocab but an artifact for it has already been registered.
[NeMo W 2025-06-14 03:35:21 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/lightning/pytorch/loops/fit_loop.py:298: The number of training batches (142) is smaller than the logging interval Trainer(log_every_n_steps=2000). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.
    
[NeMo W 2025-06-14 03:35:24 nemo_logging:405] Provided RNNT Joint tensor is of dtype torch.float16, but RNNT loss could not be calculated in fp16 due to following reason stated below. Loss will be calculated in fp32. 
    
    Env variable `NUMBA_CUDA_USE_NVIDIA_BINDING` is not available or has not set to `1`.Numba CUDA FP16 is supported in installed numba version.
[NeMo W 2025-06-14 03:35:26 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 03:35:26 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 03:35:27 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 1 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 03:35:27 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 4 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 03:35:27 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 1 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 03:38:36 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/numba/cuda/dispatcher.py:536: NumbaPerformanceWarning: Grid size 3 will likely result in GPU under-utilization due to low occupancy.
      warn(NumbaPerformanceWarning(msg))
    
[NeMo W 2025-06-14 04:08:39 nemo_logging:405] /home/<USER>/miniconda3/envs/parakeet/lib/python3.12/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:431: It is recommended to use `self.log('global_step', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
    
