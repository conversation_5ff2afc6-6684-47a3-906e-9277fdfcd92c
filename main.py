from time import time
import nemo.collections.asr as nemo_asr
import torch

# # <PERSON><PERSON><PERSON> hình memory efficient
# torch.backends.cudnn.benchmark = False
# torch.backends.cudnn.deterministic = True

now = time()
# Load model với FP16
asr_model = nemo_asr.models.ASRModel.from_pretrained(
    "nvidia/parakeet-tdt-0.6b-v2"
)
asr_model = asr_model.half()  # Convert to FP16

print(f"{(time() - now):.2f}")
now = time()

output = asr_model.transcribe(['en.wav'], timestamps=True, batch_size=64)
print(f"{(time() - now):.2f}")

now = time()
output = asr_model.transcribe(['audio.mp3'], timestamps=True, batch_size=64)
print(f"{(time() - now):.2f}")

# # by default, timestamps are enabled for char, word and segment level
# word_timestamps = output[0].timestamp['word'] # word level timestamps for first sample
# segment_timestamps = output[0].timestamp['segment'] # segment level timestamps
# char_timestamps = output[0].timestamp['char'] # char level timestamps

# for stamp in segment_timestamps:
#     print(f"{stamp['start']}s - {stamp['end']}s : {stamp['segment']}")
