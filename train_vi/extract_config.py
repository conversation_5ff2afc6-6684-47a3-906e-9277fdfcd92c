import nemo.collections.asr as nemo_asr
from omegaconf import OmegaConf

# Tải model từ Hugging Face về bộ nhớ (chỉ tải cấu trúc, không cần trọng số đầy đủ lúc này)
# Thao tác này sẽ tải model về cache của NeMo
model = nemo_asr.models.EncDecRNNTBPEModel.from_pretrained(model_name="nvidia/parakeet-tdt-0.6b-v2")

# Trích xuất cấu hình từ model
config = model.cfg

# Lưu cấu hình này vào một file YAML để chỉnh sửa
config_path = 'parakeet_tdt_0.6b_finetune.yaml'
OmegaConf.save(config, config_path)

print(f"Đã lưu file cấu hình gốc vào: {config_path}")

