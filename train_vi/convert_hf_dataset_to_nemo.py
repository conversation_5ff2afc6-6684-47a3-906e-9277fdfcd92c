import json
import os
from datasets import load_dataset, Audio
import soundfile
import librosa
from tqdm import tqdm

def create_nemo_manifest(dataset_name, subset_name, split_name, output_dir):
    # Load dataset từ HuggingFace
    dataset = load_dataset("mozilla-foundation/common_voice_17_0", "vi", split=split_name, streaming=True)

    # Loại bỏ các cột không cần thiết tr<PERSON>ớc khi cast
    columns_to_remove = ['sentence_id', 'sentence_domain']
    for col in columns_to_remove:
        if col in dataset.features:
            dataset = dataset.remove_columns([col])

    # Cast audio column về format cần thiết
    dataset = dataset.cast_column("audio", Audio(16000, mono=True))
    
    # L<PERSON>y split cụ thể (test, train, validation...)
    # split_dataset = dataset[split_name]
    
    # Rename column nếu cần
    # if subset_name in split_dataset.features:
    #     split_dataset = split_dataset.rename_column(subset_name, "text")
    
    # T<PERSON><PERSON> thư mục output
    output_dir = os.path.join(output_dir, split_name)
    os.makedirs(output_dir, exist_ok=True)

    manifest_path = os.path.join(output_dir, f"{split_name}_manifest.json")
    
    dataset_index = 0
    with open(manifest_path, 'w', encoding='utf-8') as manifest_file:
        # Lặp qua từng sample trong dataset streaming
        for sample in tqdm(dataset, desc=f"Processing {split_name}"):
            try:
                # Tạo đường dẫn cho file audio
                audio_filename = f"{split_name}_{dataset_index:09d}.wav"
                audio_filepath = os.path.join(output_dir, audio_filename)

                # Lưu file audio
                soundfile.write(
                    audio_filepath,
                    sample['audio']['array'],
                    samplerate=16000,
                    format='wav'
                )

                # Tính duration
                duration = librosa.get_duration(
                    y=sample['audio']['array'],
                    sr=sample['audio']['sampling_rate']
                )

                # Tạo manifest entry
                manifest_entry = {
                    "audio_filepath": os.path.abspath(audio_filepath),
                    "text": sample['sentence'],  # Sử dụng 'sentence' thay vì 'text'
                    "duration": duration
                }

                # Thêm metadata khác nếu có
                for key, value in sample.items():
                    if key not in ['audio', 'sentence', 'duration']:
                        manifest_entry[key] = value

                # Ghi vào manifest file (mỗi dòng là một JSON object)
                manifest_file.write(json.dumps(manifest_entry, ensure_ascii=False) + '\n')

                dataset_index += 1

                # Giới hạn số lượng sample để test (có thể bỏ comment này)
                if dataset_index >= 10:  # Chỉ xử lý 10 sample đầu để test
                    break

            except Exception as e:
                print(f"Error processing sample {dataset_index}: {e}")
                dataset_index += 1
                continue

        # Lặp qua từng sample trong split
        # for idx, sample in enumerate(tqdm(split_dataset)):
        #     try:
        #         # Tạo đường dẫn cho file audio
        #         audio_filename = f"{split_name}_{idx:06d}.wav"
        #         audio_filepath = os.path.join(output_dir, audio_filename)
                
        #         # Lưu file audio
        #         soundfile.write(
        #             audio_filepath, 
        #             sample['audio']['array'], 
        #             samplerate=16000, 
        #             format='wav'
        #         )
                
        #         # Tính duration
        #         # duration = librosa.get_duration(
        #         #     y=sample['audio']['array'], 
        #         #     sr=sample['audio']['sampling_rate']
        #         # )
                
        #         # Tạo manifest entry
        #         manifest_entry = {
        #             "audio_filepath": os.path.abspath(audio_filepath),
        #             "text": sample['text'],
        #             "duration": sample['duration']
        #         }

        #         # Thêm metadata khác nếu có
        #         for key, value in sample.items():
        #             if key not in ['audio', 'text', 'duration']:
        #                 manifest_entry[key] = value
                
        #         # Ghi vào manifest file (mỗi dòng là một JSON object)
        #         manifest_file.write(json.dumps(manifest_entry, ensure_ascii=False) + '\n')
        #     except Exception as e:
        #         print(f"Error processing sample {idx}: {e}")
        #         continue


create_nemo_manifest(
    dataset_name="mozilla-foundation/common_voice_17_0",
    subset_name="text",
    split_name="train",
    output_dir="./mozilla-foundation/common_voice_17_0"
)

# create_nemo_manifest(
#     dataset_name="leduckhai/VietMed-NER",
#     subset_name="text",
#     split_name="test",
#     output_dir="./datasets/leduckhai/VietMed-NER-test"
# )
# create_nemo_manifest(
#     dataset_name="leduckhai/VietMed-NER",
#     subset_name="text",
#     split_name="validation",
#     output_dir="./datasets/leduckhai/VietMed-NER-val"
# )
