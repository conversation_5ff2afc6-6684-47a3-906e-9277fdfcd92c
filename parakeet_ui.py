import gradio as gr
import nemo.collections.asr as nemo_asr
from time import time 

import torchaudio
from speechbrain.inference.classifiers import EncoderClassifier

language_id = EncoderClassifier.from_hparams(source="speechbrain/lang-id-voxlingua107-ecapa", run_opts={"device":"cuda"})

# Load model once
asr_model = nemo_asr.models.ASRModel.from_pretrained(model_name="nvidia/parakeet-tdt-0.6b-v2")
asr_model = asr_model.half()
asr_model = asr_model.cuda()

def transcribe_audio(audio, timestamps=False):
    if audio is None:
        return "Please upload a valid audio file."
    start_time = time()
    output = asr_model.transcribe([audio], timestamps=timestamps)
    end_time = time()
    print(f"Transcription time: {end_time - start_time:.2f} seconds")
    start_time_prediction = time()
    signal = language_id.load_audio(audio)
    prediction =  language_id.classify_batch(signal)
    end_time_prediction = time()
    print(f"prediction time: {end_time_prediction - start_time_prediction:.5f} seconds")
    print(prediction[3])
    
    if timestamps:
        segments = output[0].timestamp['segment']
        result = ""
        for seg in segments:
            result += f"{seg['start']}s - {seg['end']}s: {seg['segment']}\n"
        return f"exe: {end_time - start_time:.2f} - {result}"
    else:
        return f"exe: {end_time - start_time:.2f} - {output[0].text}"

# Build the UI
with gr.Blocks() as demo:
    gr.Markdown("# 🎙️ Parakeet-TDT-0.6B Speech to Text Demo")
    
    with gr.Row():
        audio_input = gr.Audio(type="filepath", label="Upload Audio (16kHz .wav preferred)")
    
    with gr.Row():
        timestamp_checkbox = gr.Checkbox(label="Enable Timestamps", value=False)

    with gr.Row():
        output_text = gr.Textbox(label="Transcription Output", lines=10)

    submit_btn = gr.Button("Transcribe")

    submit_btn.click(fn=transcribe_audio, inputs=[audio_input, timestamp_checkbox], outputs=output_text)

# Launch the UI
demo.launch(server_name="0.0.0.0", server_port=8001)